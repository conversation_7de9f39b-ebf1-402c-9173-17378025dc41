# Supabase Setup Guide

## Database Schema Setup

To set up the database schema in your Supabase project, follow these steps:

### 1. Access Supabase SQL Editor

1. Go to your Supabase project dashboard: https://zbghmeumbgegfcjvhkyo.supabase.co
2. Navigate to the "SQL Editor" tab in the left sidebar
3. Create a new query

### 2. Execute the Schema

Copy and paste the contents of `src/database/schema-supabase.sql` into the SQL editor and execute it.

This will create:
- User profiles table (extends Supabase auth)
- Projects table
- Project files table
- Builds table
- AI sessions table
- Project collaborators table
- Appropriate indexes
- Row Level Security (RLS) policies
- Automatic timestamp update triggers

### 3. Verify Setup

After running the schema, verify that the following tables exist:
- `public.user_profiles`
- `public.projects`
- `public.project_files`
- `public.builds`
- `public.ai_sessions`
- `public.project_collaborators`

### 4. Authentication Setup

Supabase handles authentication automatically. The schema includes:
- RLS policies to ensure users can only access their own data
- Proper foreign key relationships to `auth.users`
- Automatic profile creation triggers (if needed)

### 5. API Configuration

Your Supabase configuration is already set up in the backend:
- URL: https://zbghmeumbgegfcjvhkyo.supabase.co
- Anon Key: (configured in .env)

## Testing the Setup

Once the schema is created, you can test the backend by:

1. Starting the backend server: `npm run dev`
2. Testing the endpoints: `node test-endpoints.js`
3. The backend will now use Supabase for all database operations

## Migration from SQLite

If you have existing data in SQLite, you'll need to:
1. Export data from SQLite
2. Transform it to match the new schema
3. Import it into Supabase

The backend will automatically detect the database type from the `DB_TYPE` environment variable and use the appropriate configuration.
