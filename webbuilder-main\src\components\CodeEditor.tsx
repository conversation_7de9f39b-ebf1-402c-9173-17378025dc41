import React, { useRef, useEffect, useState, useCallback } from 'react';
import Editor, { Monaco } from '@monaco-editor/react';
import { editor } from 'monaco-editor';
import { io, Socket } from 'socket.io-client';

interface CodeEditorProps {
  projectId: string;
  filePath: string;
  language: 'typescript' | 'javascript' | 'json' | 'css' | 'html';
  initialValue?: string;
  onCodeChange?: (code: string) => void;
  onSave?: (code: string) => void;
  readOnly?: boolean;
  theme?: 'vs-dark' | 'light';
}

interface EditorError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  projectId,
  filePath,
  language,
  initialValue = '',
  onCodeChange,
  onSave,
  readOnly = false,
  theme = 'vs-dark'
}) => {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const monacoRef = useRef<Monaco | null>(null);
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [errors, setErrors] = useState<EditorError[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize WebSocket connection
  useEffect(() => {
    const socket = io('http://localhost:3002', {
      transports: ['websocket']
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      setIsConnected(true);
      socket.emit('join-project', projectId);
    });

    socket.on('disconnect', () => {
      setIsConnected(false);
    });

    socket.on('code-change', (data: { filePath: string; content: string; userId: string }) => {
      if (data.filePath === filePath && editorRef.current) {
        const currentValue = editorRef.current.getValue();
        if (currentValue !== data.content) {
          editorRef.current.setValue(data.content);
        }
      }
    });

    socket.on('file-error', (data: { filePath: string; errors: EditorError[] }) => {
      if (data.filePath === filePath) {
        setErrors(data.errors);
        updateErrorMarkers(data.errors);
      }
    });

    return () => {
      socket.disconnect();
    };
  }, [projectId, filePath]);

  // Configure Monaco Editor with React Native IntelliSense
  const handleEditorWillMount = useCallback((monaco: Monaco) => {
    monacoRef.current = monaco;

    // Configure TypeScript compiler options
    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2020,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      reactNamespace: 'React',
      allowJs: true,
      typeRoots: ['node_modules/@types']
    });

    // Add comprehensive React Native type definitions
    const reactNativeTypes = `
      declare module 'react-native' {
        // Core Components
        export interface ViewProps {
          style?: ViewStyle | ViewStyle[];
          children?: React.ReactNode;
          testID?: string;
          accessible?: boolean;
          accessibilityLabel?: string;
          accessibilityHint?: string;
          accessibilityRole?: string;
          onLayout?: (event: LayoutChangeEvent) => void;
          onTouchStart?: (event: GestureResponderEvent) => void;
          onTouchEnd?: (event: GestureResponderEvent) => void;
          pointerEvents?: 'auto' | 'none' | 'box-none' | 'box-only';
        }
        
        export interface TextProps {
          style?: TextStyle | TextStyle[];
          children?: React.ReactNode;
          numberOfLines?: number;
          onPress?: () => void;
          onLongPress?: () => void;
          selectable?: boolean;
          allowFontScaling?: boolean;
          ellipsizeMode?: 'head' | 'middle' | 'tail' | 'clip';
          adjustsFontSizeToFit?: boolean;
          minimumFontScale?: number;
        }
        
        export interface TouchableOpacityProps {
          style?: ViewStyle | ViewStyle[];
          children?: React.ReactNode;
          onPress?: () => void;
          onPressIn?: () => void;
          onPressOut?: () => void;
          onLongPress?: () => void;
          disabled?: boolean;
          activeOpacity?: number;
          delayPressIn?: number;
          delayPressOut?: number;
          delayLongPress?: number;
          hitSlop?: Insets;
          pressRetentionOffset?: Insets;
        }
        
        export interface ScrollViewProps {
          style?: ViewStyle | ViewStyle[];
          contentContainerStyle?: ViewStyle | ViewStyle[];
          children?: React.ReactNode;
          horizontal?: boolean;
          showsVerticalScrollIndicator?: boolean;
          showsHorizontalScrollIndicator?: boolean;
          pagingEnabled?: boolean;
          scrollEnabled?: boolean;
          bounces?: boolean;
          bouncesZoom?: boolean;
          alwaysBounceHorizontal?: boolean;
          alwaysBounceVertical?: boolean;
          centerContent?: boolean;
          automaticallyAdjustContentInsets?: boolean;
          decelerationRate?: 'fast' | 'normal' | number;
          directionalLockEnabled?: boolean;
          canCancelContentTouches?: boolean;
          keyboardDismissMode?: 'none' | 'interactive' | 'on-drag';
          keyboardShouldPersistTaps?: 'always' | 'never' | 'handled';
          maximumZoomScale?: number;
          minimumZoomScale?: number;
          onScroll?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
          onScrollBeginDrag?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
          onScrollEndDrag?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
          onMomentumScrollBegin?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
          onMomentumScrollEnd?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
          refreshControl?: React.ReactElement;
          removeClippedSubviews?: boolean;
          scrollEventThrottle?: number;
          snapToInterval?: number;
          snapToAlignment?: 'start' | 'center' | 'end';
          zoomScale?: number;
        }
        
        export interface ImageProps {
          source: ImageSourcePropType;
          style?: ImageStyle | ImageStyle[];
          resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
          onLoad?: (event: NativeSyntheticEvent<ImageLoadEvent>) => void;
          onError?: (error: NativeSyntheticEvent<ImageErrorEvent>) => void;
          onLoadStart?: () => void;
          onLoadEnd?: () => void;
          onProgress?: (event: NativeSyntheticEvent<ImageProgressEvent>) => void;
          defaultSource?: ImageSourcePropType;
          loadingIndicatorSource?: ImageSourcePropType;
          fadeDuration?: number;
          progressiveRenderingEnabled?: boolean;
          borderRadius?: number;
          borderTopLeftRadius?: number;
          borderTopRightRadius?: number;
          borderBottomLeftRadius?: number;
          borderBottomRightRadius?: number;
        }
        
        export interface TextInputProps {
          style?: TextStyle | TextStyle[];
          value?: string;
          defaultValue?: string;
          onChangeText?: (text: string) => void;
          onSubmitEditing?: (event: NativeSyntheticEvent<TextInputSubmitEditingEventData>) => void;
          onFocus?: (event: NativeSyntheticEvent<TextInputFocusEventData>) => void;
          onBlur?: (event: NativeSyntheticEvent<TextInputFocusEventData>) => void;
          onSelectionChange?: (event: NativeSyntheticEvent<TextInputSelectionChangeEventData>) => void;
          placeholder?: string;
          placeholderTextColor?: string;
          secureTextEntry?: boolean;
          multiline?: boolean;
          numberOfLines?: number;
          autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
          autoCorrect?: boolean;
          autoFocus?: boolean;
          blurOnSubmit?: boolean;
          caretHidden?: boolean;
          contextMenuHidden?: boolean;
          editable?: boolean;
          keyboardType?: 'default' | 'numeric' | 'email-address' | 'ascii-capable' | 'numbers-and-punctuation' | 'url' | 'number-pad' | 'phone-pad' | 'name-phone-pad' | 'decimal-pad' | 'twitter' | 'web-search' | 'visible-password';
          returnKeyType?: 'done' | 'go' | 'next' | 'search' | 'send' | 'none' | 'previous' | 'default' | 'emergency-call' | 'google' | 'join' | 'route' | 'yahoo';
          maxLength?: number;
          selectTextOnFocus?: boolean;
          selection?: { start: number; end?: number };
          selectionColor?: string;
          textAlign?: 'left' | 'center' | 'right';
          textAlignVertical?: 'auto' | 'top' | 'bottom' | 'center';
          underlineColorAndroid?: string;
          clearButtonMode?: 'never' | 'while-editing' | 'unless-editing' | 'always';
          clearTextOnFocus?: boolean;
          dataDetectorTypes?: DataDetectorTypes | DataDetectorTypes[];
          enablesReturnKeyAutomatically?: boolean;
          keyboardAppearance?: 'default' | 'light' | 'dark';
          onKeyPress?: (event: NativeSyntheticEvent<TextInputKeyPressEventData>) => void;
          onContentSizeChange?: (event: NativeSyntheticEvent<TextInputContentSizeChangeEventData>) => void;
          onEndEditing?: (event: NativeSyntheticEvent<TextInputEndEditingEventData>) => void;
          returnKeyLabel?: string;
          spellCheck?: boolean;
        }
        
        export interface FlatListProps<ItemT> {
          data: ReadonlyArray<ItemT> | null | undefined;
          renderItem: (info: { item: ItemT; index: number; separators: any }) => React.ReactElement | null;
          keyExtractor?: (item: ItemT, index: number) => string;
          ItemSeparatorComponent?: React.ComponentType<any> | null;
          ListEmptyComponent?: React.ComponentType<any> | React.ReactElement | null;
          ListFooterComponent?: React.ComponentType<any> | React.ReactElement | null;
          ListHeaderComponent?: React.ComponentType<any> | React.ReactElement | null;
          columnWrapperStyle?: ViewStyle;
          extraData?: any;
          getItemLayout?: (data: ArrayLike<ItemT> | null | undefined, index: number) => { length: number; offset: number; index: number };
          horizontal?: boolean;
          initialNumToRender?: number;
          initialScrollIndex?: number;
          inverted?: boolean;
          numColumns?: number;
          onEndReached?: (info: { distanceFromEnd: number }) => void;
          onEndReachedThreshold?: number;
          onRefresh?: () => void;
          onViewableItemsChanged?: (info: { viewableItems: ViewToken[]; changed: ViewToken[] }) => void;
          refreshing?: boolean;
          removeClippedSubviews?: boolean;
          viewabilityConfig?: ViewabilityConfig;
          scrollEnabled?: boolean;
          showsHorizontalScrollIndicator?: boolean;
          showsVerticalScrollIndicator?: boolean;
        }
        
        export interface SectionListProps<ItemT, SectionT> {
          sections: ReadonlyArray<SectionListData<ItemT, SectionT>>;
          renderItem: (info: { item: ItemT; index: number; section: SectionListData<ItemT, SectionT>; separators: any }) => React.ReactElement | null;
          renderSectionHeader?: (info: { section: SectionListData<ItemT, SectionT> }) => React.ReactElement | null;
          renderSectionFooter?: (info: { section: SectionListData<ItemT, SectionT> }) => React.ReactElement | null;
          ItemSeparatorComponent?: React.ComponentType<any> | null;
          SectionSeparatorComponent?: React.ComponentType<any> | null;
          ListEmptyComponent?: React.ComponentType<any> | React.ReactElement | null;
          ListFooterComponent?: React.ComponentType<any> | React.ReactElement | null;
          ListHeaderComponent?: React.ComponentType<any> | React.ReactElement | null;
          keyExtractor?: (item: ItemT, index: number) => string;
          extraData?: any;
          initialNumToRender?: number;
          inverted?: boolean;
          onEndReached?: (info: { distanceFromEnd: number }) => void;
          onEndReachedThreshold?: number;
          onRefresh?: () => void;
          refreshing?: boolean;
          removeClippedSubviews?: boolean;
          stickySectionHeadersEnabled?: boolean;
        }
        
        // Component exports
        export const View: React.ComponentType<ViewProps>;
        export const Text: React.ComponentType<TextProps>;
        export const TouchableOpacity: React.ComponentType<TouchableOpacityProps>;
        export const ScrollView: React.ComponentType<ScrollViewProps>;
        export const Image: React.ComponentType<ImageProps>;
        export const TextInput: React.ComponentType<TextInputProps>;
        export const FlatList: React.ComponentType<FlatListProps<any>>;
        export const SectionList: React.ComponentType<SectionListProps<any, any>>;
        
        // Additional Components
        export const SafeAreaView: React.ComponentType<ViewProps>;
        export const KeyboardAvoidingView: React.ComponentType<ViewProps & { behavior?: 'height' | 'position' | 'padding'; keyboardVerticalOffset?: number }>;
        export const Modal: React.ComponentType<{ visible?: boolean; animationType?: 'none' | 'slide' | 'fade'; transparent?: boolean; onRequestClose?: () => void; children?: React.ReactNode }>;
        export const ActivityIndicator: React.ComponentType<{ size?: 'small' | 'large' | number; color?: string; animating?: boolean; hidesWhenStopped?: boolean }>;
        export const Switch: React.ComponentType<{ value?: boolean; onValueChange?: (value: boolean) => void; disabled?: boolean; trackColor?: { false?: string; true?: string }; thumbColor?: string; ios_backgroundColor?: string }>;
        export const Slider: React.ComponentType<{ value?: number; minimumValue?: number; maximumValue?: number; step?: number; onValueChange?: (value: number) => void; onSlidingComplete?: (value: number) => void; disabled?: boolean; minimumTrackTintColor?: string; maximumTrackTintColor?: string; thumbTintColor?: string }>;
        export const Picker: React.ComponentType<{ selectedValue?: any; onValueChange?: (itemValue: any, itemIndex: number) => void; enabled?: boolean; mode?: 'dialog' | 'dropdown'; prompt?: string; itemStyle?: TextStyle; children?: React.ReactNode }>;
        export const DatePickerIOS: React.ComponentType<{ date?: Date; onDateChange?: (date: Date) => void; maximumDate?: Date; minimumDate?: Date; mode?: 'date' | 'time' | 'datetime'; minuteInterval?: 1 | 2 | 3 | 4 | 5 | 6 | 10 | 12 | 15 | 20 | 30; timeZoneOffsetInMinutes?: number }>;
        export const ProgressBarAndroid: React.ComponentType<{ styleAttr?: 'Horizontal' | 'Normal' | 'Small' | 'Large' | 'Inverse' | 'SmallInverse' | 'LargeInverse'; indeterminate?: boolean; progress?: number; color?: string }>;
        export const ProgressViewIOS: React.ComponentType<{ progress?: number; progressTintColor?: string; trackTintColor?: string; progressImage?: ImageSourcePropType; trackImage?: ImageSourcePropType }>;
        export const RefreshControl: React.ComponentType<{ refreshing?: boolean; onRefresh?: () => void; colors?: string[]; enabled?: boolean; progressBackgroundColor?: string; progressViewOffset?: number; size?: 'default' | 'large'; tintColor?: string; title?: string; titleColor?: string }>;
        export const StatusBar: React.ComponentType<{ animated?: boolean; backgroundColor?: string; barStyle?: 'default' | 'light-content' | 'dark-content'; hidden?: boolean; networkActivityIndicatorVisible?: boolean; showHideTransition?: 'fade' | 'slide'; translucent?: boolean }>;
        export const WebView: React.ComponentType<{ source?: { uri?: string; html?: string; baseUrl?: string }; style?: ViewStyle; renderError?: (errorDomain: string, errorCode: number, errorDesc: string) => React.ReactElement; renderLoading?: () => React.ReactElement; onLoad?: () => void; onLoadEnd?: () => void; onLoadStart?: () => void; onError?: (event: any) => void; automaticallyAdjustContentInsets?: boolean; contentInset?: Insets; onNavigationStateChange?: (navState: any) => void; startInLoadingState?: boolean; scalesPageToFit?: boolean; domStorageEnabled?: boolean; javaScriptEnabled?: boolean; mixedContentMode?: 'never' | 'always' | 'compatibility'; thirdPartyCookiesEnabled?: boolean; userAgent?: string; allowsInlineMediaPlayback?: boolean; bounces?: boolean; dataDetectorTypes?: DataDetectorTypes | DataDetectorTypes[]; scrollEnabled?: boolean; geolocationEnabled?: boolean; allowUniversalAccessFromFileURLs?: boolean; allowFileAccessFromFileURLs?: boolean; originWhitelist?: string[]; injectedJavaScript?: string; mediaPlaybackRequiresUserAction?: boolean; nativeConfig?: any; onMessage?: (event: any) => void; onShouldStartLoadWithRequest?: (event: any) => boolean; postMessage?: (data: string) => void }>;
        
        // StyleSheet
        export const StyleSheet: {
          create<T extends NamedStyles<T> | NamedStyles<any>>(styles: T | NamedStyles<T>): T;
          flatten<T>(style?: RegisteredStyle<T>): T;
          hairlineWidth: number;
          absoluteFill: RegisteredStyle<ViewStyle>;
          absoluteFillObject: ViewStyle;
          compose<T>(style1: RegisteredStyle<T>, style2: RegisteredStyle<T>): RegisteredStyle<T>;
        };
        
        // Dimensions
        export const Dimensions: {
          get(dim: 'window' | 'screen'): ScaledSize;
          addEventListener(type: 'change', handler: (dims: { window: ScaledSize; screen: ScaledSize }) => void): void;
          removeEventListener(type: 'change', handler: (dims: { window: ScaledSize; screen: ScaledSize }) => void): void;
        };
        
        // Platform
        export const Platform: {
          OS: 'ios' | 'android' | 'web' | 'windows' | 'macos';
          Version: string | number;
          isPad?: boolean;
          isTVOS?: boolean;
          isTV?: boolean;
          isTesting?: boolean;
          select<T>(specifics: { ios?: T; android?: T; web?: T; windows?: T; macos?: T; native?: T; default?: T }): T;
        };
        
        // PixelRatio
        export const PixelRatio: {
          get(): number;
          getFontScale(): number;
          getPixelSizeForLayoutSize(layoutSize: number): number;
          roundToNearestPixel(layoutSize: number): number;
        };
        
        // Alert
        export const Alert: {
          alert(title: string, message?: string, buttons?: AlertButton[], options?: AlertOptions): void;
          prompt(title: string, message?: string, callbackOrButtons?: ((text: string) => void) | AlertButton[], type?: AlertType, defaultValue?: string, keyboardType?: string): void;
        };
        
        // Animated
        export const Animated: {
          View: React.ComponentType<any>;
          Text: React.ComponentType<any>;
          Image: React.ComponentType<any>;
          ScrollView: React.ComponentType<any>;
          FlatList: React.ComponentType<any>;
          SectionList: React.ComponentType<any>;
          Value: new (value: number) => AnimatedValue;
          ValueXY: new (valueIn?: { x: number | AnimatedValue; y: number | AnimatedValue }) => AnimatedValueXY;
          timing: (value: AnimatedValue | AnimatedValueXY, config: TimingAnimationConfig) => CompositeAnimation;
          spring: (value: AnimatedValue | AnimatedValueXY, config: SpringAnimationConfig) => CompositeAnimation;
          decay: (value: AnimatedValue | AnimatedValueXY, config: DecayAnimationConfig) => CompositeAnimation;
          sequence: (animations: CompositeAnimation[]) => CompositeAnimation;
          parallel: (animations: CompositeAnimation[], config?: ParallelConfig) => CompositeAnimation;
          stagger: (time: number, animations: CompositeAnimation[]) => CompositeAnimation;
          delay: (time: number) => CompositeAnimation;
          loop: (animation: CompositeAnimation, config?: LoopAnimationConfig) => CompositeAnimation;
          event: (argMapping: any[], config?: EventConfig) => (...args: any[]) => void;
          createAnimatedComponent: <T>(Component: T) => T;
          add: (a: AnimatedAddition, b: AnimatedAddition) => AnimatedAddition;
          subtract: (a: AnimatedSubtraction, b: AnimatedSubtraction) => AnimatedSubtraction;
          divide: (a: AnimatedDivision, b: AnimatedDivision) => AnimatedDivision;
          multiply: (a: AnimatedMultiplication, b: AnimatedMultiplication) => AnimatedMultiplication;
          modulo: (a: AnimatedModulo, b: AnimatedModulo) => AnimatedModulo;
          diffClamp: (a: AnimatedNode, min: number, max: number) => AnimatedDiffClamp;
          interpolate: (value: AnimatedNode, config: InterpolationConfigType) => AnimatedInterpolation;
        };
        
        // Easing
        export const Easing: {
          step0: (n: number) => number;
          step1: (n: number) => number;
          linear: (t: number) => number;
          ease: (t: number) => number;
          quad: (t: number) => number;
          cubic: (t: number) => number;
          poly: (n: number) => (t: number) => number;
          sin: (t: number) => number;
          circle: (t: number) => number;
          exp: (t: number) => number;
          elastic: (bounciness?: number) => (t: number) => number;
          back: (s?: number) => (t: number) => number;
          bounce: (t: number) => number;
          bezier: (x1: number, y1: number, x2: number, y2: number) => (t: number) => number;
          in: (easing: (t: number) => number) => (t: number) => number;
          out: (easing: (t: number) => number) => (t: number) => number;
          inOut: (easing: (t: number) => number) => (t: number) => number;
        };
        
        // Keyboard
        export const Keyboard: {
          addListener: (eventName: KeyboardEventName, callback: KeyboardEventListener) => EmitterSubscription;
          removeListener: (eventName: KeyboardEventName, callback: KeyboardEventListener) => void;
          removeAllListeners: (eventName?: KeyboardEventName) => void;
          dismiss: () => void;
        };
        
        // Linking
        export const Linking: {
          addEventListener: (type: string, handler: (event: { url: string }) => void) => void;
          removeEventListener: (type: string, handler: (event: { url: string }) => void) => void;
          openURL: (url: string) => Promise<any>;
          canOpenURL: (url: string) => Promise<boolean>;
          getInitialURL: () => Promise<string | null>;
        };
        
        // NetInfo
        export const NetInfo: {
          isConnected: {
            addEventListener: (eventName: string, handler: (isConnected: boolean) => void) => void;
            removeEventListener: (eventName: string, handler: (isConnected: boolean) => void) => void;
            fetch: () => Promise<boolean>;
          };
          addEventListener: (eventName: string, handler: (connectionInfo: any) => void) => void;
          removeEventListener: (eventName: string, handler: (connectionInfo: any) => void) => void;
          getConnectionInfo: () => Promise<any>;
        };
        
        // PanResponder
        export const PanResponder: {
          create: (config: PanResponderCallbacks) => PanResponderInstance;
        };
        
        // Vibration
        export const Vibration: {
          vibrate: (pattern?: number | number[], repeat?: boolean) => void;
          cancel: () => void;
        };
        
        // Type definitions
        export type RegisteredStyle<T> = number & { __registeredStyleBrand: T };
        export type NamedStyles<T> = { [P in keyof T]: ViewStyle | TextStyle | ImageStyle };
        
        export interface ScaledSize {
          width: number;
          height: number;
          scale: number;
          fontScale: number;
        }
        
        export interface Insets {
          top?: number;
          left?: number;
          bottom?: number;
          right?: number;
        }
        
        export interface LayoutChangeEvent {
          nativeEvent: {
            layout: {
              x: number;
              y: number;
              width: number;
              height: number;
            };
          };
        }
        
        export interface GestureResponderEvent {
          nativeEvent: {
            changedTouches: any[];
            identifier: number;
            locationX: number;
            locationY: number;
            pageX: number;
            pageY: number;
            target: number;
            timestamp: number;
            touches: any[];
          };
        }
        
        export interface NativeSyntheticEvent<T> {
          nativeEvent: T;
          currentTarget: number;
          target: number;
          bubbles?: boolean;
          cancelable?: boolean;
          defaultPrevented?: boolean;
          eventPhase?: number;
          isTrusted?: boolean;
          preventDefault(): void;
          isDefaultPrevented(): boolean;
          stopPropagation(): void;
          isPropagationStopped(): boolean;
          persist(): void;
          timeStamp: number;
          type: string;
        }
        
        export interface NativeScrollEvent {
          contentInset: { bottom: number; left: number; right: number; top: number };
          contentOffset: { x: number; y: number };
          contentSize: { height: number; width: number };
          layoutMeasurement: { height: number; width: number };
          zoomScale?: number;
        }
        
        export interface ImageLoadEvent {
          source: {
            height: number;
            width: number;
            uri: string;
          };
        }
        
        export interface ImageErrorEvent {
          error: any;
        }
        
        export interface ImageProgressEvent {
          loaded: number;
          total: number;
        }
        
        export type ImageSourcePropType = { uri: string; bundle?: string; method?: string; headers?: { [key: string]: string }; body?: string; cache?: 'default' | 'reload' | 'force-cache' | 'only-if-cached'; width?: number; height?: number; scale?: number } | number | any[];
        
        export interface TextInputSubmitEditingEventData {
          text: string;
        }
        
        export interface TextInputFocusEventData {
          target: number;
        }
        
        export interface TextInputSelectionChangeEventData {
          selection: {
            start: number;
            end: number;
          };
        }
        
        export interface TextInputKeyPressEventData {
          key: string;
        }
        
        export interface TextInputContentSizeChangeEventData {
          contentSize: {
            width: number;
            height: number;
          };
        }
        
        export interface TextInputEndEditingEventData {
          text: string;
        }
        
        export type DataDetectorTypes = 'phoneNumber' | 'link' | 'email' | 'none' | 'all';
        
        export interface ViewToken {
          item: any;
          key: string;
          index: number | null;
          isViewable: boolean;
          section?: any;
        }
        
        export interface ViewabilityConfig {
          minimumViewTime?: number;
          viewAreaCoveragePercentThreshold?: number;
          itemVisiblePercentThreshold?: number;
          waitForInteraction?: boolean;
        }
        
        export interface SectionListData<ItemT, SectionT> {
          data: ReadonlyArray<ItemT>;
          key?: string;
          renderItem?: (info: { item: ItemT; index: number; section: SectionListData<ItemT, SectionT>; separators: any }) => React.ReactElement | null;
          ItemSeparatorComponent?: React.ComponentType<any> | null;
          keyExtractor?: (item: ItemT, index: number) => string;
        }
        
        export interface AlertButton {
          text?: string;
          onPress?: () => void;
          style?: 'default' | 'cancel' | 'destructive';
        }
        
        export interface AlertOptions {
          cancelable?: boolean;
          onDismiss?: () => void;
        }
        
        export type AlertType = 'default' | 'plain-text' | 'secure-text' | 'login-password';
        
        // Style interfaces
        export interface ViewStyle {
          alignContent?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'space-between' | 'space-around';
          alignItems?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';
          alignSelf?: 'auto' | 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';
          aspectRatio?: number;
          backfaceVisibility?: 'visible' | 'hidden';
          backgroundColor?: string;
          borderBottomColor?: string;
          borderBottomEndRadius?: number;
          borderBottomLeftRadius?: number;
          borderBottomRightRadius?: number;
          borderBottomStartRadius?: number;
          borderBottomWidth?: number;
          borderColor?: string;
          borderEndColor?: string;
          borderEndWidth?: number;
          borderLeftColor?: string;
          borderLeftWidth?: number;
          borderRadius?: number;
          borderRightColor?: string;
          borderRightWidth?: number;
          borderStartColor?: string;
          borderStartWidth?: number;
          borderStyle?: 'solid' | 'dotted' | 'dashed';
          borderTopColor?: string;
          borderTopEndRadius?: number;
          borderTopLeftRadius?: number;
          borderTopRightRadius?: number;
          borderTopStartRadius?: number;
          borderTopWidth?: number;
          borderWidth?: number;
          bottom?: number | string;
          display?: 'none' | 'flex';
          elevation?: number;
          end?: number | string;
          flex?: number;
          flexBasis?: number | string;
          flexDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
          flexGrow?: number;
          flexShrink?: number;
          flexWrap?: 'wrap' | 'nowrap' | 'wrap-reverse';
          height?: number | string;
          justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
          left?: number | string;
          margin?: number | string;
          marginBottom?: number | string;
          marginEnd?: number | string;
          marginHorizontal?: number | string;
          marginLeft?: number | string;
          marginRight?: number | string;
          marginStart?: number | string;
          marginTop?: number | string;
          marginVertical?: number | string;
          maxHeight?: number | string;
          maxWidth?: number | string;
          minHeight?: number | string;
          minWidth?: number | string;
          opacity?: number;
          overflow?: 'visible' | 'hidden' | 'scroll';
          padding?: number | string;
          paddingBottom?: number | string;
          paddingEnd?: number | string;
          paddingHorizontal?: number | string;
          paddingLeft?: number | string;
          paddingRight?: number | string;
          paddingStart?: number | string;
          paddingTop?: number | string;
          paddingVertical?: number | string;
          position?: 'absolute' | 'relative';
          right?: number | string;
          start?: number | string;
          top?: number | string;
          transform?: any[];
          transformMatrix?: number[];
          rotation?: number;
          scaleX?: number;
          scaleY?: number;
          translateX?: number;
          translateY?: number;
          width?: number | string;
          zIndex?: number;
          direction?: 'inherit' | 'ltr' | 'rtl';
          shadowColor?: string;
          shadowOffset?: { width: number; height: number };
          shadowOpacity?: number;
          shadowRadius?: number;
        }
        
        export interface TextStyle extends ViewStyle {
          color?: string;
          fontFamily?: string;
          fontSize?: number;
          fontStyle?: 'normal' | 'italic';
          fontWeight?: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';
          letterSpacing?: number;
          lineHeight?: number;
          textAlign?: 'auto' | 'left' | 'right' | 'center' | 'justify';
          textAlignVertical?: 'auto' | 'top' | 'bottom' | 'center';
          textDecorationLine?: 'none' | 'underline' | 'line-through' | 'underline line-through';
          textDecorationStyle?: 'solid' | 'double' | 'dotted' | 'dashed';
          textDecorationColor?: string;
          textShadowColor?: string;
          textShadowOffset?: { width: number; height: number };
          textShadowRadius?: number;
          textTransform?: 'none' | 'capitalize' | 'uppercase' | 'lowercase';
          writingDirection?: 'auto' | 'ltr' | 'rtl';
        }
        
        export interface ImageStyle extends ViewStyle {
          resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
          tintColor?: string;
          overlayColor?: string;
        }
        
        // Animation types
        export interface AnimatedValue {
          setValue(value: number): void;
          setOffset(offset: number): void;
          flattenOffset(): void;
          extractOffset(): void;
          addListener(callback: (value: { value: number }) => void): string;
          removeListener(id: string): void;
          removeAllListeners(): void;
          stopAnimation(callback?: (value: number) => void): void;
          resetAnimation(callback?: (value: number) => void): void;
          interpolate(config: InterpolationConfigType): AnimatedInterpolation;
        }
        
        export interface AnimatedValueXY {
          x: AnimatedValue;
          y: AnimatedValue;
          setValue(value: { x: number; y: number }): void;
          setOffset(offset: { x: number; y: number }): void;
          flattenOffset(): void;
          extractOffset(): void;
          stopAnimation(callback?: (value: { x: number; y: number }) => void): void;
          resetAnimation(callback?: (value: { x: number; y: number }) => void): void;
          addListener(callback: (value: { x: number; y: number }) => void): string;
          removeListener(id: string): void;
          removeAllListeners(): void;
          getLayout(): { left: AnimatedValue; top: AnimatedValue };
          getTranslateTransform(): { transform: { translateX: AnimatedValue; translateY: AnimatedValue }[] };
        }
        
        export interface CompositeAnimation {
          start(callback?: (finished: { finished: boolean }) => void): void;
          stop(): void;
          reset(): void;
        }
        
        export interface TimingAnimationConfig {
          toValue: number | AnimatedValue | { x: number; y: number } | AnimatedValueXY;
          easing?: (value: number) => number;
          duration?: number;
          delay?: number;
          useNativeDriver?: boolean;
        }
        
        export interface SpringAnimationConfig {
          toValue: number | AnimatedValue | { x: number; y: number } | AnimatedValueXY;
          restDisplacementThreshold?: number;
          overshootClamping?: boolean;
          restSpeedThreshold?: number;
          velocity?: number | { x: number; y: number };
          bounciness?: number;
          speed?: number;
          tension?: number;
          friction?: number;
          stiffness?: number;
          damping?: number;
          mass?: number;
          delay?: number;
          useNativeDriver?: boolean;
        }
        
        export interface DecayAnimationConfig {
          velocity: number | { x: number; y: number };
          deceleration?: number;
          delay?: number;
          useNativeDriver?: boolean;
        }
        
        export interface ParallelConfig {
          stopTogether?: boolean;
        }
        
        export interface LoopAnimationConfig {
          iterations?: number;
          resetBeforeIteration?: boolean;
        }
        
        export interface EventConfig {
          listener?: (...args: any[]) => void;
          useNativeDriver?: boolean;
        }
        
        export interface InterpolationConfigType {
          inputRange: number[];
          outputRange: string[] | number[];
          easing?: (input: number) => number;
          extrapolate?: 'extend' | 'identity' | 'clamp';
          extrapolateLeft?: 'extend' | 'identity' | 'clamp';
          extrapolateRight?: 'extend' | 'identity' | 'clamp';
        }
        
        export interface AnimatedInterpolation {
          interpolate(config: InterpolationConfigType): AnimatedInterpolation;
        }
        
        export interface AnimatedNode {}
        export interface AnimatedAddition extends AnimatedNode {}
        export interface AnimatedSubtraction extends AnimatedNode {}
        export interface AnimatedDivision extends AnimatedNode {}
        export interface AnimatedMultiplication extends AnimatedNode {}
        export interface AnimatedModulo extends AnimatedNode {}
        export interface AnimatedDiffClamp extends AnimatedNode {}
        
        // Keyboard types
        export type KeyboardEventName = 'keyboardWillShow' | 'keyboardDidShow' | 'keyboardWillHide' | 'keyboardDidHide' | 'keyboardWillChangeFrame' | 'keyboardDidChangeFrame';
        export type KeyboardEventListener = (event: KeyboardEvent) => void;
        
        export interface KeyboardEvent {
          duration?: number;
          easing?: string;
          endCoordinates?: {
            width: number;
            height: number;
            screenX: number;
            screenY: number;
          };
          startCoordinates?: {
            width: number;
            height: number;
            screenX: number;
            screenY: number;
          };
        }
        
        export interface EmitterSubscription {
          remove(): void;
        }
        
        // PanResponder types
        export interface PanResponderCallbacks {
          onMoveShouldSetPanResponder?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => boolean;
          onMoveShouldSetPanResponderCapture?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => boolean;
          onStartShouldSetPanResponder?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => boolean;
          onStartShouldSetPanResponderCapture?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => boolean;
          onPanResponderReject?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => void;
          onPanResponderGrant?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => void;
          onPanResponderStart?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => void;
          onPanResponderEnd?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => void;
          onPanResponderRelease?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => void;
          onPanResponderMove?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => void;
          onPanResponderTerminate?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => void;
          onPanResponderTerminationRequest?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => boolean;
          onShouldBlockNativeResponder?: (evt: GestureResponderEvent, gestureState: PanResponderGestureState) => boolean;
        }
        
        export interface PanResponderGestureState {
          stateID: number;
          moveX: number;
          moveY: number;
          x0: number;
          y0: number;
          dx: number;
          dy: number;
          vx: number;
          vy: number;
          numberActiveTouches: number;
        }
        
        export interface PanResponderInstance {
          panHandlers: any;
        }
      }
      
      declare module 'react' {
        export interface Component<P = {}, S = {}> {
          props: Readonly<P>;
          state: Readonly<S>;
        }
        
        export class Component<P, S> {
          constructor(props: P);
          setState<K extends keyof S>(state: ((prevState: Readonly<S>, props: Readonly<P>) => (Pick<S, K> | S | null)) | (Pick<S, K> | S | null), callback?: () => void): void;
          render(): ReactNode;
        }
        
        export function useState<T>(initialState: T | (() => T)): [T, (value: T | ((prev: T) => T)) => void];
        export function useEffect(effect: () => void | (() => void), deps?: any[]): void;
        export function useCallback<T extends (...args: any[]) => any>(callback: T, deps: any[]): T;
        export function useMemo<T>(factory: () => T, deps: any[]): T;
        export function useRef<T>(initialValue: T): { current: T };
        export function useContext<T>(context: Context<T>): T;
        export function useReducer<R extends Reducer<any, any>>(reducer: R, initialState: ReducerState<R>, initializer?: undefined): [ReducerState<R>, Dispatch<ReducerAction<R>>];
        export function useImperativeHandle<T, R extends T>(ref: Ref<T> | undefined, init: () => R, deps?: DependencyList): void;
        export function useLayoutEffect(effect: EffectCallback, deps?: DependencyList): void;
        export function useDebugValue<T>(value: T, format?: (value: T) => any): void;
        
        export type ReactNode = string | number | boolean | null | undefined | ReactElement | ReactNode[];
        export interface ReactElement {
          type: any;
          props: any;
          key: string | number | null;
        }
        
        export const createElement: (type: any, props?: any, ...children: any[]) => ReactElement;
        export const Fragment: ExoticComponent<{ children?: ReactNode }>;
        
        export type FC<P = {}> = FunctionComponent<P>;
        export interface FunctionComponent<P = {}> {
          (props: PropsWithChildren<P>, context?: any): ReactElement<any, any> | null;
          propTypes?: WeakValidationMap<P>;
          contextTypes?: ValidationMap<any>;
          defaultProps?: Partial<P>;
          displayName?: string;
        }
        
        export type ComponentType<P = {}> = ComponentClass<P> | FunctionComponent<P>;
        export interface ComponentClass<P = {}, S = ComponentState> extends StaticLifecycle<P, S> {
          new (props: P, context?: any): Component<P, S>;
          propTypes?: WeakValidationMap<P>;
          contextType?: Context<any>;
          contextTypes?: ValidationMap<any>;
          childContextTypes?: ValidationMap<any>;
          defaultProps?: Partial<P>;
          displayName?: string;
        }
        
        export type PropsWithChildren<P> = P & { children?: ReactNode };
        export type ComponentState = any;
        export type Key = string | number;
        export type Ref<T> = RefCallback<T> | RefObject<T> | null;
        export type RefCallback<T> = (instance: T | null) => void;
        export interface RefObject<T> {
          readonly current: T | null;
        }
        export type DependencyList = ReadonlyArray<any>;
        export type EffectCallback = () => (void | (() => void | undefined));
        export interface Context<T> {
          Provider: Provider<T>;
          Consumer: Consumer<T>;
          displayName?: string;
        }
        export type Provider<T> = ComponentType<ProviderProps<T>>;
        export type Consumer<T> = ComponentType<ConsumerProps<T>>;
        export interface ProviderProps<T> {
          value: T;
          children?: ReactNode;
        }
        export interface ConsumerProps<T> {
          children: (value: T) => ReactNode;
        }
        export type Reducer<S, A> = (prevState: S, action: A) => S;
        export type ReducerState<R extends Reducer<any, any>> = R extends Reducer<infer S, any> ? S : never;
        export type ReducerAction<R extends Reducer<any, any>> = R extends Reducer<any, infer A> ? A : never;
        export type Dispatch<A> = (value: A) => void;
        export interface StaticLifecycle<P, S> {
          getDerivedStateFromProps?: GetDerivedStateFromProps<P, S>;
          getDerivedStateFromError?: GetDerivedStateFromError<P, S>;
        }
        export type GetDerivedStateFromProps<P, S> = (nextProps: Readonly<P>, prevState: S) => Partial<S> | null;
        export type GetDerivedStateFromError<P, S> = (error: any) => Partial<S> | null;
        export type ValidationMap<T> = { [K in keyof T]?: Validator<T[K]> };
        export type WeakValidationMap<T> = { [K in keyof T]?: null extends T[K] ? Validator<T[K] | null | undefined> : undefined extends T[K] ? Validator<T[K] | null | undefined> : Validator<T[K]> };
        export interface Validator<T> {
          (props: object, propName: string, componentName: string, location: string, propFullName: string): Error | null;
        }
        export type ExoticComponent<P = {}> = (props: P) => ReactElement | null;
      }
    `;

    monaco.languages.typescript.typescriptDefaults.addExtraLib(
      reactNativeTypes,
      'file:///node_modules/@types/react-native/index.d.ts'
    );

    // Add React types
    const reactTypes = `
      declare namespace React {
        type FC<P = {}> = (props: P) => ReactElement | null;
        type ComponentType<P = {}> = FC<P> | ComponentClass<P>;
        
        interface ComponentClass<P = {}> {
          new (props: P): Component<P, any>;
        }
      }
    `;

    monaco.languages.typescript.typescriptDefaults.addExtraLib(
      reactTypes,
      'file:///node_modules/@types/react/index.d.ts'
    );

    // Configure diagnostics
    monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: false,
      noSyntaxValidation: false,
      noSuggestionDiagnostics: false
    });

    // Add comprehensive React Native snippets and IntelliSense
    monaco.languages.registerCompletionItemProvider('typescript', {
      provideCompletionItems: (model, position) => {
        const word = model.getWordUntilPosition(position);
        const range = {
          startLineNumber: position.lineNumber,
          endLineNumber: position.lineNumber,
          startColumn: word.startColumn,
          endColumn: word.endColumn
        };

        const suggestions = [
          // Component Snippets
          {
            label: 'rnfc',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'import React from \'react\';',
              'import { View, Text, StyleSheet } from \'react-native\';',
              '',
              'interface ${1:ComponentName}Props {',
              '  ${2:// Add your props here}',
              '}',
              '',
              'const ${1:ComponentName}: React.FC<${1:ComponentName}Props> = (${3:props}) => {',
              '  return (',
              '    <View style={styles.container}>',
              '      <Text style={styles.text}>${4:Hello World}</Text>',
              '    </View>',
              '  );',
              '};',
              '',
              'const styles = StyleSheet.create({',
              '  container: {',
              '    flex: 1,',
              '    justifyContent: \'center\',',
              '    alignItems: \'center\',',
              '    backgroundColor: \'#fff\',',
              '  },',
              '  text: {',
              '    fontSize: 16,',
              '    color: \'#333\',',
              '  },',
              '});',
              '',
              'export default ${1:ComponentName};'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native Functional Component with TypeScript',
            range
          },
          {
            label: 'rncc',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'import React, { Component } from \'react\';',
              'import { View, Text, StyleSheet } from \'react-native\';',
              '',
              'interface ${1:ComponentName}Props {',
              '  ${2:// Add your props here}',
              '}',
              '',
              'interface ${1:ComponentName}State {',
              '  ${3:// Add your state here}',
              '}',
              '',
              'class ${1:ComponentName} extends Component<${1:ComponentName}Props, ${1:ComponentName}State> {',
              '  constructor(props: ${1:ComponentName}Props) {',
              '    super(props);',
              '    this.state = {',
              '      ${4:// Initialize state}',
              '    };',
              '  }',
              '',
              '  render() {',
              '    return (',
              '      <View style={styles.container}>',
              '        <Text style={styles.text}>${5:Hello World}</Text>',
              '      </View>',
              '    );',
              '  }',
              '}',
              '',
              'const styles = StyleSheet.create({',
              '  container: {',
              '    flex: 1,',
              '    justifyContent: \'center\',',
              '    alignItems: \'center\',',
              '    backgroundColor: \'#fff\',',
              '  },',
              '  text: {',
              '    fontSize: 16,',
              '    color: \'#333\',',
              '  },',
              '});',
              '',
              'export default ${1:ComponentName};'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native Class Component with TypeScript',
            range
          },
          {
            label: 'rnhook',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'import { useState, useEffect } from \'react\';',
              '',
              'const use${1:CustomHook} = (${2:initialValue}) => {',
              '  const [${3:state}, set${3/(.*)/${3:/capitalize}/}] = useState(${2:initialValue});',
              '',
              '  useEffect(() => {',
              '    ${4:// Effect logic here}',
              '  }, [${3:state}]);',
              '',
              '  return {',
              '    ${3:state},',
              '    set${3/(.*)/${3:/capitalize}/},',
              '    ${5:// Additional return values}',
              '  };',
              '};',
              '',
              'export default use${1:CustomHook};'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Custom React Hook',
            range
          },
          {
            label: 'rnstyle',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'const styles = StyleSheet.create({',
              '  ${1:container}: {',
              '    ${2:flex: 1,}',
              '    ${3:backgroundColor: \'#fff\',}',
              '  },',
              '});'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native StyleSheet',
            range
          },
          
          // Component Snippets
          {
            label: 'rnview',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: '<View style={${1:styles.container}}>\n  ${2}\n</View>',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native View Component',
            range
          },
          {
            label: 'rntext',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: '<Text style={${1:styles.text}}>${2:Text content}</Text>',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native Text Component',
            range
          },
          {
            label: 'rntouchable',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              '<TouchableOpacity',
              '  style={${1:styles.button}}',
              '  onPress={${2:handlePress}}',
              '  activeOpacity={${3:0.7}}',
              '>',
              '  <Text style={${4:styles.buttonText}}>${5:Button Text}</Text>',
              '</TouchableOpacity>'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native TouchableOpacity Component',
            range
          },
          {
            label: 'rnscroll',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              '<ScrollView',
              '  style={${1:styles.scrollView}}',
              '  contentContainerStyle={${2:styles.contentContainer}}',
              '  showsVerticalScrollIndicator={${3:false}}',
              '>',
              '  ${4}',
              '</ScrollView>'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native ScrollView Component',
            range
          },
          {
            label: 'rnimage',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              '<Image',
              '  source={${1:{ uri: \'https://example.com/image.jpg\' }}}',
              '  style={${2:styles.image}}',
              '  resizeMode="${3:cover}"',
              '/>'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native Image Component',
            range
          },
          {
            label: 'rninput',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              '<TextInput',
              '  style={${1:styles.input}}',
              '  value={${2:value}}',
              '  onChangeText={${3:setValue}}',
              '  placeholder="${4:Enter text...}"',
              '  placeholderTextColor="${5:#999}"',
              '/>'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native TextInput Component',
            range
          },
          {
            label: 'rnflatlist',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              '<FlatList',
              '  data={${1:data}}',
              '  renderItem={({ item, index }) => (',
              '    <View style={${2:styles.item}}>',
              '      <Text>${3:{item.title}}</Text>',
              '    </View>',
              '  )}',
              '  keyExtractor={(item, index) => ${4:index.toString()}}',
              '  showsVerticalScrollIndicator={${5:false}}',
              '/>'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native FlatList Component',
            range
          },
          {
            label: 'rnsafearea',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              '<SafeAreaView style={${1:styles.safeArea}}>',
              '  ${2}',
              '</SafeAreaView>'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native SafeAreaView Component',
            range
          },
          {
            label: 'rnmodal',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              '<Modal',
              '  visible={${1:modalVisible}}',
              '  animationType="${2:slide}"',
              '  transparent={${3:true}}',
              '  onRequestClose={() => ${4:setModalVisible(false)}}',
              '>',
              '  <View style={${5:styles.modalContainer}}>',
              '    <View style={${6:styles.modalContent}}>',
              '      ${7}',
              '    </View>',
              '  </View>',
              '</Modal>'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native Modal Component',
            range
          },
          
          // Hook Snippets
          {
            label: 'usestate',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'const [${1:state}, set${1/(.*)/${1:/capitalize}/}] = useState(${2:initialValue});',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React useState Hook',
            range
          },
          {
            label: 'useeffect',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'useEffect(() => {',
              '  ${1:// Effect logic here}',
              '  ',
              '  return () => {',
              '    ${2:// Cleanup logic here}',
              '  };',
              '}, [${3:dependencies}]);'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React useEffect Hook',
            range
          },
          {
            label: 'usecallback',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'const ${1:memoizedCallback} = useCallback(',
              '  (${2:args}) => {',
              '    ${3:// Callback logic here}',
              '  },',
              '  [${4:dependencies}]',
              ');'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React useCallback Hook',
            range
          },
          {
            label: 'usememo',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'const ${1:memoizedValue} = useMemo(',
              '  () => ${2:computeExpensiveValue()},',
              '  [${3:dependencies}]',
              ');'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React useMemo Hook',
            range
          },
          {
            label: 'useref',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'const ${1:ref} = useRef(${2:initialValue});',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React useRef Hook',
            range
          },
          
          // Navigation Snippets (React Navigation)
          {
            label: 'rnnavigation',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'import { useNavigation } from \'@react-navigation/native\';',
              '',
              'const navigation = useNavigation();',
              '',
              '// Navigate to screen',
              'navigation.navigate(\'${1:ScreenName}\', { ${2:params} });',
              '',
              '// Go back',
              'navigation.goBack();',
              '',
              '// Replace current screen',
              'navigation.replace(\'${3:ScreenName}\');'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Navigation Hook Usage',
            range
          },
          
          // Animated Snippets
          {
            label: 'rnanimated',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'import { Animated } from \'react-native\';',
              '',
              'const ${1:animatedValue} = new Animated.Value(${2:0});',
              '',
              'const ${3:animateIn} = () => {',
              '  Animated.timing(${1:animatedValue}, {',
              '    toValue: ${4:1},',
              '    duration: ${5:300},',
              '    useNativeDriver: ${6:true},',
              '  }).start();',
              '};'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React Native Animated API',
            range
          },
          
          // Style Snippets
          {
            label: 'flexcenter',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'justifyContent: \'center\',',
              'alignItems: \'center\','
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Flex Center Alignment',
            range
          },
          {
            label: 'flexrow',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'flexDirection: \'row\',',
              'alignItems: \'center\','
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Flex Row Layout',
            range
          },
          {
            label: 'shadow',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'shadowColor: \'#000\',',
              'shadowOffset: {',
              '  width: 0,',
              '  height: ${1:2},',
              '},',
              'shadowOpacity: ${2:0.25},',
              'shadowRadius: ${3:3.84},',
              'elevation: ${4:5},'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Shadow Styles for iOS and Android',
            range
          }
        ];

        return { suggestions };
      }
    });

    setIsLoading(false);
  }, []);

  // Handle editor mount
  const handleEditorDidMount = useCallback((editor: editor.IStandaloneCodeEditor, monaco: Monaco) => {
    editorRef.current = editor;

    // Configure editor options
    editor.updateOptions({
      fontSize: 14,
      fontFamily: 'JetBrains Mono, Consolas, Monaco, monospace',
      lineHeight: 1.6,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      automaticLayout: true,
      suggestOnTriggerCharacters: true,
      quickSuggestions: {
        other: true,
        comments: true,
        strings: true
      },
      parameterHints: { enabled: true },
      formatOnPaste: true,
      formatOnType: true
    });

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      const value = editor.getValue();
      onSave?.(value);
      saveToBackend(value);
    });

    // Auto-save on changes
    editor.onDidChangeModelContent(() => {
      const value = editor.getValue();
      onCodeChange?.(value);
      
      // Debounced save to backend
      clearTimeout(window.autoSaveTimeout);
      window.autoSaveTimeout = setTimeout(() => {
        saveToBackend(value);
      }, 1000);
    });

    // Validate code on changes
    editor.onDidChangeModelContent(() => {
      validateCode();
    });

    // Initial validation
    validateCode();
  }, [onCodeChange, onSave]);

  // Update error markers in editor
  const updateErrorMarkers = useCallback((errors: EditorError[]) => {
    if (!editorRef.current || !monacoRef.current) return;

    const markers = errors.map(error => ({
      startLineNumber: error.line,
      startColumn: error.column,
      endLineNumber: error.line,
      endColumn: error.column + 10,
      message: error.message,
      severity: error.severity === 'error' 
        ? monacoRef.current!.MarkerSeverity.Error
        : error.severity === 'warning'
        ? monacoRef.current!.MarkerSeverity.Warning
        : monacoRef.current!.MarkerSeverity.Info
    }));

    monacoRef.current.editor.setModelMarkers(
      editorRef.current.getModel()!,
      'typescript',
      markers
    );
  }, []);

  // Validate code using TypeScript compiler
  const validateCode = useCallback(async () => {
    if (!editorRef.current || !monacoRef.current) return;

    const model = editorRef.current.getModel();
    if (!model) return;

    try {
      // Get TypeScript diagnostics
      const markers = await monacoRef.current.languages.typescript.getTypeScriptWorker()
        .then(worker => worker(model.uri))
        .then(client => client.getSemanticDiagnostics(model.uri.toString()));

      const errors: EditorError[] = markers.map(marker => ({
        line: marker.startLineNumber || 1,
        column: marker.startColumn || 1,
        message: marker.messageText as string,
        severity: marker.severity === 8 ? 'error' : marker.severity === 4 ? 'warning' : 'info'
      }));

      setErrors(errors);
      updateErrorMarkers(errors);

      // Send errors to backend for processing
      if (socketRef.current && errors.length > 0) {
        socketRef.current.emit('file-validation', {
          projectId,
          filePath,
          errors
        });
      }
    } catch (error) {
      console.error('Validation error:', error);
    }
  }, [projectId, filePath]);

  // Save code to backend
  const saveToBackend = useCallback(async (code: string) => {
    if (!socketRef.current) return;

    try {
      socketRef.current.emit('code-change', {
        projectId,
        filePath,
        content: code,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Save error:', error);
    }
  }, [projectId, filePath]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-900">
        <div className="text-gray-400">Loading editor...</div>
      </div>
    );
  }

  return (
    <div className="h-full relative">
      {/* Connection status indicator */}
      <div className="absolute top-2 right-2 z-10 flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`} />
        <span className="text-xs text-gray-400">
          {isConnected ? 'Connected' : 'Disconnected'}
        </span>
      </div>

      {/* Error count indicator */}
      {errors.length > 0 && (
        <div className="absolute top-2 left-2 z-10 bg-red-600/20 backdrop-blur-sm border border-red-500/50 rounded px-2 py-1">
          <span className="text-xs text-red-400">
            {errors.length} error{errors.length !== 1 ? 's' : ''}
          </span>
        </div>
      )}

      <Editor
        height="100%"
        language={language}
        theme={theme}
        value={initialValue}
        beforeMount={handleEditorWillMount}
        onMount={handleEditorDidMount}
        options={{
          readOnly,
          selectOnLineNumbers: true,
          roundedSelection: false,
          cursorStyle: 'line',
          automaticLayout: true,
          glyphMargin: true,
          folding: true,
          lineNumbers: 'on',
          lineDecorationsWidth: 10,
          lineNumbersMinChars: 3,
          renderLineHighlight: 'all',
          scrollbar: {
            vertical: 'visible',
            horizontal: 'visible',
            useShadows: false,
            verticalHasArrows: false,
            horizontalHasArrows: false
          }
        }}
      />
    </div>
  );
};

// Extend window for auto-save timeout
declare global {
  interface Window {
    autoSaveTimeout: number;
  }
}

export default CodeEditor;