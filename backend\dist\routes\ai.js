"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const aiService_1 = require("../services/aiService");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const rateLimiter_1 = require("../middleware/rateLimiter");
const logger_1 = require("../utils/logger");
const joi_1 = __importDefault(require("joi"));
const router = (0, express_1.Router)();
const generateCodeSchema = joi_1.default.object({
    prompt: joi_1.default.string().required().min(1).max(2000),
    projectId: joi_1.default.string().required().uuid(),
    sessionId: joi_1.default.string().optional().uuid(),
    filePath: joi_1.default.string().optional(),
    language: joi_1.default.string().optional().valid('typescript', 'javascript', 'json'),
    context: joi_1.default.object({
        existingCode: joi_1.default.string().optional(),
        dependencies: joi_1.default.array().items(joi_1.default.string()).optional(),
        targetPlatform: joi_1.default.string().optional().valid('ios', 'android', 'web'),
    }).optional(),
});
const explainCodeSchema = joi_1.default.object({
    code: joi_1.default.string().required().min(1).max(10000),
    language: joi_1.default.string().required().valid('typescript', 'javascript', 'json', 'jsx', 'tsx'),
    filePath: joi_1.default.string().optional(),
    projectId: joi_1.default.string().required().uuid(),
    sessionId: joi_1.default.string().optional().uuid(),
});
const fixCodeSchema = joi_1.default.object({
    code: joi_1.default.string().required().min(1).max(10000),
    error: joi_1.default.string().required().min(1).max(1000),
    language: joi_1.default.string().required().valid('typescript', 'javascript', 'json', 'jsx', 'tsx'),
    filePath: joi_1.default.string().required(),
    projectId: joi_1.default.string().required().uuid(),
    sessionId: joi_1.default.string().optional().uuid(),
});
const modifyCodeSchema = joi_1.default.object({
    code: joi_1.default.string().required().min(1).max(10000),
    instruction: joi_1.default.string().required().min(1).max(1000),
    language: joi_1.default.string().required().valid('typescript', 'javascript', 'json', 'jsx', 'tsx'),
    filePath: joi_1.default.string().required(),
    projectId: joi_1.default.string().required().uuid(),
    sessionId: joi_1.default.string().optional().uuid(),
});
const chatSchema = joi_1.default.object({
    message: joi_1.default.string().required().min(1).max(2000),
    projectId: joi_1.default.string().required().uuid(),
    sessionId: joi_1.default.string().optional().uuid(),
    includeContext: joi_1.default.boolean().optional().default(false),
});
const suggestImprovementsSchema = joi_1.default.object({
    code: joi_1.default.string().required().min(1).max(10000),
    language: joi_1.default.string().required().valid('typescript', 'javascript', 'json', 'jsx', 'tsx'),
    filePath: joi_1.default.string().required(),
});
router.use(rateLimiter_1.rateLimit);
router.get('/health', async (req, res) => {
    try {
        const health = await aiService_1.aiService.healthCheck();
        res.json({
            success: true,
            ...health
        });
    }
    catch (error) {
        logger_1.logger.error('AI health check error:', error);
        res.status(500).json({
            success: false,
            error: 'Health check failed'
        });
    }
});
router.post('/generate', auth_1.authenticateToken, (0, validation_1.validateRequest)(generateCodeSchema), async (req, res) => {
    try {
        const request = req.body;
        logger_1.logger.info(`Code generation request from user ${req.user?.id}`, {
            projectId: request.projectId,
            prompt: request.prompt.substring(0, 100),
        });
        const generatedCode = await aiService_1.aiService.generateCode(request);
        const response = {
            success: true,
            generatedCode,
            sessionId: request.sessionId || generatedCode.filePath || 'default',
        };
        logger_1.logger.info(`Code generation completed for user ${req.user?.id}`, {
            projectId: request.projectId,
            codeLength: generatedCode.code.length,
            confidence: generatedCode.confidence,
        });
        res.json(response);
    }
    catch (error) {
        logger_1.logger.error('Error in code generation:', error);
        const response = {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to generate code',
            sessionId: req.body.sessionId || 'default',
        };
        res.status(500).json(response);
    }
});
router.post('/explain', auth_1.authenticateToken, (0, validation_1.validateRequest)(explainCodeSchema), async (req, res) => {
    try {
        const request = req.body;
        logger_1.logger.info(`Code explanation request from user ${req.user?.id}`, {
            projectId: request.projectId,
            language: request.language,
            codeLength: request.code.length,
        });
        const explanation = await aiService_1.aiService.explainCode(request);
        const response = {
            success: true,
            explanation,
            sessionId: request.sessionId || 'default',
        };
        res.json(response);
    }
    catch (error) {
        logger_1.logger.error('Error in code explanation:', error);
        const response = {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to explain code',
            sessionId: req.body.sessionId || 'default',
        };
        res.status(500).json(response);
    }
});
router.post('/fix', auth_1.authenticateToken, (0, validation_1.validateRequest)(fixCodeSchema), async (req, res) => {
    try {
        const request = req.body;
        logger_1.logger.info(`Code fix request from user ${req.user?.id}`, {
            projectId: request.projectId,
            filePath: request.filePath,
            error: request.error.substring(0, 100),
        });
        const codeFix = await aiService_1.aiService.fixCode(request);
        const response = {
            success: true,
            codeFix,
            sessionId: request.sessionId || 'default',
        };
        res.json(response);
    }
    catch (error) {
        logger_1.logger.error('Error in code fixing:', error);
        const response = {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to fix code',
            sessionId: req.body.sessionId || 'default',
        };
        res.status(500).json(response);
    }
});
router.post('/modify', (0, validation_1.validateRequest)(modifyCodeSchema), async (req, res) => {
    try {
        const request = req.body;
        logger_1.logger.info(`Code modification request from user ${req.user?.id}`, {
            projectId: request.projectId,
            filePath: request.filePath,
            instruction: request.instruction.substring(0, 100),
        });
        const codeDiff = await aiService_1.aiService.modifyCode(request);
        const response = {
            success: true,
            codeDiff,
            sessionId: request.sessionId || 'default',
        };
        res.json(response);
    }
    catch (error) {
        logger_1.logger.error('Error in code modification:', error);
        const response = {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to modify code',
            sessionId: req.body.sessionId || 'default',
        };
        res.status(500).json(response);
    }
});
router.post('/chat', (0, validation_1.validateRequest)(chatSchema), async (req, res) => {
    try {
        const request = req.body;
        logger_1.logger.info(`AI chat request from user ${req.user?.id || 'anonymous'}`, {
            projectId: request.projectId,
            message: request.message.substring(0, 100),
            includeContext: request.includeContext,
        });
        const chatResponse = await aiService_1.aiService.chat(request);
        const response = {
            success: true,
            response: chatResponse,
            sessionId: request.sessionId || 'default',
        };
        res.json(response);
    }
    catch (error) {
        logger_1.logger.error('Error in AI chat:', error);
        const response = {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to process chat',
            sessionId: req.body.sessionId || 'default',
        };
        res.status(500).json(response);
    }
});
router.post('/suggest', (0, validation_1.validateRequest)(suggestImprovementsSchema), async (req, res) => {
    try {
        const { code, language, filePath } = req.body;
        logger_1.logger.info(`Code suggestions request from user ${req.user?.id}`, {
            language,
            filePath,
            codeLength: code.length,
        });
        const suggestions = await aiService_1.aiService.suggestImprovements(code, language, filePath);
        res.json({
            success: true,
            suggestions,
        });
    }
    catch (error) {
        logger_1.logger.error('Error in code suggestions:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to get suggestions',
            suggestions: [],
        });
    }
});
router.get('/session/:sessionId/history', async (req, res) => {
    try {
        const { sessionId } = req.params;
        if (!sessionId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(sessionId)) {
            res.status(400).json({
                success: false,
                error: 'Invalid session ID format',
            });
            return;
        }
        const history = await aiService_1.aiService.getSessionHistory(sessionId);
        res.json({
            success: true,
            history,
        });
    }
    catch (error) {
        logger_1.logger.error('Error getting session history:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to get session history',
        });
    }
});
router.delete('/session/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;
        if (!sessionId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(sessionId)) {
            res.status(400).json({
                success: false,
                error: 'Invalid session ID format',
            });
            return;
        }
        await aiService_1.aiService.clearSession(sessionId);
        res.json({
            success: true,
            message: 'Session cleared successfully',
        });
    }
    catch (error) {
        logger_1.logger.error('Error clearing session:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to clear session',
        });
    }
});
exports.default = router;
//# sourceMappingURL=ai.js.map