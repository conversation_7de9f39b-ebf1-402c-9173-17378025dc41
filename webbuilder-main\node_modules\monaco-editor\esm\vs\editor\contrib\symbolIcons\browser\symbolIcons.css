/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-editor .codicon.codicon-symbol-array,
.monaco-workbench .codicon.codicon-symbol-array { color: var(--vscode-symbolIcon-arrayForeground); }
.monaco-editor .codicon.codicon-symbol-boolean,
.monaco-workbench .codicon.codicon-symbol-boolean { color: var(--vscode-symbolIcon-booleanForeground); }
.monaco-editor .codicon.codicon-symbol-class,
.monaco-workbench .codicon.codicon-symbol-class { color: var(--vscode-symbolIcon-classForeground); }
.monaco-editor .codicon.codicon-symbol-method,
.monaco-workbench .codicon.codicon-symbol-method { color: var(--vscode-symbolIcon-methodForeground); }
.monaco-editor .codicon.codicon-symbol-color,
.monaco-workbench .codicon.codicon-symbol-color { color: var(--vscode-symbolIcon-colorForeground); }
.monaco-editor .codicon.codicon-symbol-constant,
.monaco-workbench .codicon.codicon-symbol-constant { color: var(--vscode-symbolIcon-constantForeground); }
.monaco-editor .codicon.codicon-symbol-constructor,
.monaco-workbench .codicon.codicon-symbol-constructor { color: var(--vscode-symbolIcon-constructorForeground); }
.monaco-editor .codicon.codicon-symbol-value,
.monaco-workbench .codicon.codicon-symbol-value,
.monaco-editor .codicon.codicon-symbol-enum,
.monaco-workbench .codicon.codicon-symbol-enum { color: var(--vscode-symbolIcon-enumeratorForeground); }
.monaco-editor .codicon.codicon-symbol-enum-member,
.monaco-workbench .codicon.codicon-symbol-enum-member { color: var(--vscode-symbolIcon-enumeratorMemberForeground); }
.monaco-editor .codicon.codicon-symbol-event,
.monaco-workbench .codicon.codicon-symbol-event { color: var(--vscode-symbolIcon-eventForeground); }
.monaco-editor .codicon.codicon-symbol-field,
.monaco-workbench .codicon.codicon-symbol-field { color: var(--vscode-symbolIcon-fieldForeground); }
.monaco-editor .codicon.codicon-symbol-file,
.monaco-workbench .codicon.codicon-symbol-file { color: var(--vscode-symbolIcon-fileForeground); }
.monaco-editor .codicon.codicon-symbol-folder,
.monaco-workbench .codicon.codicon-symbol-folder { color: var(--vscode-symbolIcon-folderForeground); }
.monaco-editor .codicon.codicon-symbol-function,
.monaco-workbench .codicon.codicon-symbol-function { color: var(--vscode-symbolIcon-functionForeground); }
.monaco-editor .codicon.codicon-symbol-interface,
.monaco-workbench .codicon.codicon-symbol-interface { color: var(--vscode-symbolIcon-interfaceForeground); }
.monaco-editor .codicon.codicon-symbol-key,
.monaco-workbench .codicon.codicon-symbol-key { color: var(--vscode-symbolIcon-keyForeground); }
.monaco-editor .codicon.codicon-symbol-keyword,
.monaco-workbench .codicon.codicon-symbol-keyword { color: var(--vscode-symbolIcon-keywordForeground); }
.monaco-editor .codicon.codicon-symbol-module,
.monaco-workbench .codicon.codicon-symbol-module { color: var(--vscode-symbolIcon-moduleForeground); }
.monaco-editor .codicon.codicon-symbol-namespace,
.monaco-workbench .codicon.codicon-symbol-namespace { color: var(--vscode-symbolIcon-namespaceForeground); }
.monaco-editor .codicon.codicon-symbol-null,
.monaco-workbench .codicon.codicon-symbol-null { color: var(--vscode-symbolIcon-nullForeground); }
.monaco-editor .codicon.codicon-symbol-number,
.monaco-workbench .codicon.codicon-symbol-number { color: var(--vscode-symbolIcon-numberForeground); }
.monaco-editor .codicon.codicon-symbol-object,
.monaco-workbench .codicon.codicon-symbol-object { color: var(--vscode-symbolIcon-objectForeground); }
.monaco-editor .codicon.codicon-symbol-operator,
.monaco-workbench .codicon.codicon-symbol-operator { color: var(--vscode-symbolIcon-operatorForeground); }
.monaco-editor .codicon.codicon-symbol-package,
.monaco-workbench .codicon.codicon-symbol-package { color: var(--vscode-symbolIcon-packageForeground); }
.monaco-editor .codicon.codicon-symbol-property,
.monaco-workbench .codicon.codicon-symbol-property { color: var(--vscode-symbolIcon-propertyForeground); }
.monaco-editor .codicon.codicon-symbol-reference,
.monaco-workbench .codicon.codicon-symbol-reference { color: var(--vscode-symbolIcon-referenceForeground); }
.monaco-editor .codicon.codicon-symbol-snippet,
.monaco-workbench .codicon.codicon-symbol-snippet { color: var(--vscode-symbolIcon-snippetForeground); }
.monaco-editor .codicon.codicon-symbol-string,
.monaco-workbench .codicon.codicon-symbol-string { color: var(--vscode-symbolIcon-stringForeground); }
.monaco-editor .codicon.codicon-symbol-struct,
.monaco-workbench .codicon.codicon-symbol-struct { color: var(--vscode-symbolIcon-structForeground); }
.monaco-editor .codicon.codicon-symbol-text,
.monaco-workbench .codicon.codicon-symbol-text { color: var(--vscode-symbolIcon-textForeground); }
.monaco-editor .codicon.codicon-symbol-type-parameter,
.monaco-workbench .codicon.codicon-symbol-type-parameter { color: var(--vscode-symbolIcon-typeParameterForeground); }
.monaco-editor .codicon.codicon-symbol-unit,
.monaco-workbench .codicon.codicon-symbol-unit { color: var(--vscode-symbolIcon-unitForeground); }
.monaco-editor .codicon.codicon-symbol-variable,
.monaco-workbench .codicon.codicon-symbol-variable { color: var(--vscode-symbolIcon-variableForeground); }
