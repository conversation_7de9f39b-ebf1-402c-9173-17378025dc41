export interface GenerateCodeRequest {
  prompt: string;
  projectId: string;
  sessionId?: string;
  filePath?: string;
  language?: string;
  context?: {
    existingCode?: string;
    dependencies?: string[];
    targetPlatform?: string;
  };
}

export interface GenerateCodeResponse {
  success: boolean;
  generatedCode?: {
    code: string;
    language: string;
    filePath?: string;
    explanation: string;
    dependencies?: string[];
    imports?: string[];
    confidence: number;
  };
  sessionId: string;
  error?: string;
  tokensUsed?: number;
}

export interface ExplainCodeRequest {
  code: string;
  language: string;
  filePath?: string;
  projectId: string;
  sessionId?: string;
}

export interface ExplainCodeResponse {
  success: boolean;
  explanation?: string;
  sessionId: string;
  error?: string;
  tokensUsed?: number;
}

export interface FixCodeRequest {
  code: string;
  error: string;
  language: string;
  filePath: string;
  projectId: string;
  sessionId?: string;
}

export interface FixCodeResponse {
  success: boolean;
  codeFix?: {
    originalCode: string;
    fixedCode: string;
    explanation: string;
    confidence: number;
    filePath: string;
  };
  sessionId: string;
  error?: string;
  tokensUsed?: number;
}

export interface ChatRequest {
  message: string;
  projectId: string;  
  sessionId?: string;
  includeContext?: boolean;
}

export interface ChatResponse {
  success: boolean;
  response?: string;
  sessionId: string;
  error?: string;
  tokensUsed?: number;
  suggestedActions?: string[];
}

export class AIService {
  private baseUrl: string;
  private authToken: string | null = null;

  constructor(baseUrl: string = 'http://localhost:3002/api') {
    this.baseUrl = baseUrl;
    this.authToken = localStorage.getItem('authToken');
  }

  setAuthToken(token: string) {
    this.authToken = token;
    localStorage.setItem('authToken', token);
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.authToken) {
      headers.Authorization = `Bearer ${this.authToken}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Generate code from natural language prompt
   */
  async generateCode(request: GenerateCodeRequest): Promise<GenerateCodeResponse> {
    return this.makeRequest<GenerateCodeResponse>('/ai/generate', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Explain provided code
   */
  async explainCode(request: ExplainCodeRequest): Promise<ExplainCodeResponse> {
    return this.makeRequest<ExplainCodeResponse>('/ai/explain', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Fix code based on error message
   */
  async fixCode(request: FixCodeRequest): Promise<FixCodeResponse> {
    return this.makeRequest<FixCodeResponse>('/ai/fix', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Chat with AI assistant
   */
  async chat(request: ChatRequest): Promise<ChatResponse> {
    return this.makeRequest<ChatResponse>('/ai/chat', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Get code improvement suggestions
   */
  async getSuggestions(code: string, language: string, filePath: string): Promise<{
    success: boolean;
    suggestions: Array<{
      type: 'performance' | 'security' | 'best-practice' | 'bug-fix';
      title: string;
      description: string;
      code?: string;
      filePath?: string;
      lineNumber?: number;
      severity: 'low' | 'medium' | 'high';
    }>;
  }> {
    return this.makeRequest('/ai/suggest', {
      method: 'POST',
      body: JSON.stringify({ code, language, filePath }),
    });
  }

  /**
   * Get conversation history for a session
   */
  async getSessionHistory(sessionId: string): Promise<{
    success: boolean;
    history: Array<{
      id: string;
      type: 'prompt' | 'response' | 'code' | 'error';
      content: string;
      timestamp: string;
      metadata?: Record<string, any>;
    }>;
  }> {
    return this.makeRequest(`/ai/session/${sessionId}/history`);
  }

  /**
   * Clear conversation session
   */
  async clearSession(sessionId: string): Promise<{ success: boolean; message: string }> {
    return this.makeRequest(`/ai/session/${sessionId}`, {
      method: 'DELETE',
    });
  }

  /**
   * Check AI service health
   */
  async getHealth(): Promise<{
    success: boolean;
    providers: {
      openai: boolean;
      anthropic: boolean;
      pinecone: boolean;
    };
    status: string;
  }> {
    return this.makeRequest('/ai/health');
  }
}

// Singleton instance
export const aiService = new AIService();
