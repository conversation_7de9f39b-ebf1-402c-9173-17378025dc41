{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAE/B,2CAAwC;AACxC,0CAA8C;AAEvC,MAAM,iBAAiB,GAAG,KAAK,EAAE,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IAC5G,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;IAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,uBAAuB;SAC/B,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QAEH,MAAM,IAAI,GAAG,MAAM,kBAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAElD,IAAI,IAAI,EAAE,CAAC;YACT,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;YAChB,IAAI,EAAE,CAAC;YACP,OAAO;QACT,CAAC;QAGD,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAW,CAAe,CAAC;QAGzE,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,OAAO,CAAC,MAAM;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,IAAI,EAAE,EAAE;YACR,gBAAgB,EAAE,MAAM;YACxB,SAAS,EAAE,EAAE;YACb,WAAW,EAAE;gBACX,KAAK,EAAE,OAAO;gBACd,cAAc,EAAE;oBACd,QAAQ,EAAE,EAAE;oBACZ,OAAO,EAAE,CAAC;oBACV,QAAQ,EAAE,IAAI;oBACd,OAAO,EAAE,IAAI;iBACd;gBACD,eAAe,EAAE,OAAO;gBACxB,UAAU,EAAE,QAAQ;aACrB;YACD,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QAElD,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,eAAe;gBACtB,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,eAAe;gBACtB,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,2BAA2B;aACnC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAlEW,QAAA,iBAAiB,qBAkE5B;AAEK,MAAM,YAAY,GAAG,CAAC,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACxF,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;IAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,IAAI,CAAC,KAAK,EAAE,CAAC;QAEX,IAAI,EAAE,CAAC;QACP,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAW,CAAe,CAAC;QAEzE,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,OAAO,CAAC,MAAM;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,IAAI,EAAE,EAAE;YACR,gBAAgB,EAAE,MAAM;YACxB,SAAS,EAAE,EAAE;YACb,WAAW,EAAE;gBACX,KAAK,EAAE,OAAO;gBACd,cAAc,EAAE;oBACd,QAAQ,EAAE,EAAE;oBACZ,OAAO,EAAE,CAAC;oBACV,QAAQ,EAAE,IAAI;oBACd,OAAO,EAAE,IAAI;iBACd;gBACD,eAAe,EAAE,OAAO;gBACxB,UAAU,EAAE,QAAQ;aACrB;YACD,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAEf,eAAM,CAAC,IAAI,CAAC,0DAA0D,EAAE,KAAK,CAAC,CAAC;QAC/E,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAC;AAxCW,QAAA,YAAY,gBAwCvB"}