export interface BundleRequest {
  projectId: string;
  platform: 'web' | 'ios' | 'android';
  entryFile?: string;
  options?: {
    dev?: boolean;
    minify?: boolean;
    sourceMaps?: boolean;
    hot?: boolean;
  };
}

export interface BundleResponse {
  success: boolean;
  bundle?: {
    id: string;
    platform: string;
    code: string;
    sourceMap?: string;
    size: number;
    buildTime: number;
    createdAt: string;
    errors: any[];
    warnings: any[];
  };
  error?: string;
  buildTime: number;
}

export interface ValidationRequest {
  projectId: string;
  filePath: string;
  content: string;
}

export interface ValidationResponse {
  success: boolean;
  validation: {
    valid: boolean;
    errors: Array<{
      type: string;
      message: string;
      filename: string;
      line: number;
      column: number;
      severity: 'error' | 'warning';
    }>;
    warnings: Array<{
      type: string;
      message: string;
      filename: string;
      line: number;
      column: number;
    }>;
  };
}

export class BundlerService {
  private baseUrl: string;
  private authToken: string | null = null;

  constructor(baseUrl: string = 'http://localhost:3002/api') {
    this.baseUrl = baseUrl;
    this.authToken = localStorage.getItem('authToken');
  }

  setAuthToken(token: string) {
    this.authToken = token;
    localStorage.setItem('authToken', token);
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.authToken) {
      headers.Authorization = `Bearer ${this.authToken}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Create a bundle for a project
   */
  async createBundle(request: BundleRequest): Promise<BundleResponse> {
    return this.makeRequest<BundleResponse>('/bundler/build', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Get cached bundle for a project and platform
   */
  async getCachedBundle(projectId: string, platform: 'web' | 'ios' | 'android'): Promise<BundleResponse> {
    return this.makeRequest<BundleResponse>(`/bundler/bundle/${projectId}/${platform}`);
  }

  /**
   * Validate code syntax and imports
   */
  async validateCode(request: ValidationRequest): Promise<ValidationResponse> {
    return this.makeRequest<ValidationResponse>('/bundler/validate', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Start file watching for a project
   */
  async startFileWatching(projectId: string): Promise<{ success: boolean; message: string }> {
    return this.makeRequest<{ success: boolean; message: string }>(`/bundler/watch/start/${projectId}`, {
      method: 'POST',
    });
  }

  /**
   * Stop file watching for a project
   */
  async stopFileWatching(projectId: string): Promise<{ success: boolean; message: string }> {
    return this.makeRequest<{ success: boolean; message: string }>(`/bundler/watch/stop/${projectId}`, {
      method: 'POST',
    });
  }

  /**
   * Clear bundle cache for a project
   */
  async clearBundleCache(projectId: string): Promise<{ success: boolean; message: string }> {
    return this.makeRequest<{ success: boolean; message: string }>(`/bundler/cache/${projectId}`, {
      method: 'DELETE',
    });
  }

  /**
   * Cleanup all bundler resources for a project
   */
  async cleanup(projectId: string): Promise<{ success: boolean; message: string }> {
    return this.makeRequest<{ success: boolean; message: string }>(`/bundler/cleanup/${projectId}`, {
      method: 'DELETE',
    });
  }

  /**
   * Get bundler service status
   */
  async getStatus(): Promise<{
    success: boolean;
    status: {
      activeWatchers: number;
      activeProcesses: number;
      cacheSize: number;
      uptime: number;
      memory: {
        rss: number;
        heapTotal: number;
        heapUsed: number;
        external: number;
        arrayBuffers: number;
      };
    };
  }> {
    return this.makeRequest<any>('/bundler/status');
  }

  /**
   * Get bundle URL for iframe preview
   */
  getBundleUrl(projectId: string, platform: 'web' | 'ios' | 'android'): string {
    return `${this.baseUrl}/bundler/bundle/${projectId}/${platform}`;
  }

  /**
   * Create WebSocket connection for real-time updates
   */
  createWebSocket(projectId: string): WebSocket {
    const wsUrl = this.baseUrl.replace('http', 'ws').replace('/api', '') + `/ws?projectId=${projectId}`;
    return new WebSocket(wsUrl);
  }
}

// Singleton instance
export const bundlerService = new BundlerService();