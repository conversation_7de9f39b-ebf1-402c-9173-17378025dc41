const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testAIChat() {
  console.log('🧪 Simple AI Chat Test...\n');

  try {
    // Test AI Chat Request
    console.log('Testing AI Chat Request...');
    const chatRequest = {
      message: 'Hello! Can you help me with React Native?',
      projectId: '123e4567-e89b-12d3-a456-426614174000',
      includeContext: true
    };

    const response = await fetch('http://localhost:3002/api/ai/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(chatRequest)
    });

    console.log('Response status:', response.status);
    const data = await response.json();
    console.log('Response data:', JSON.stringify(data, null, 2));

    if (response.ok && data.success) {
      console.log('✅ AI Chat is working!');
      console.log('Response preview:', data.response?.substring(0, 100) + '...');
    } else {
      console.log('❌ AI Chat failed');
      console.log('Error:', data.error || 'Unknown error');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAIChat();
