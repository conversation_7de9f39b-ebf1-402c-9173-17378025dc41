import { User, LoginRequest, RegisterRequest, AuthResponse } from '@/types';
export declare class SupabaseAuthService {
    static register(data: RegisterRequest): Promise<AuthResponse>;
    static login(data: LoginRequest): Promise<AuthResponse>;
    static refreshToken(refreshToken: string): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
    static logout(userId: string, refreshToken?: string): Promise<void>;
    static verifyToken(token: string): Promise<User | null>;
    static cleanupExpiredTokens(): Promise<void>;
    private static sanitizeUser;
}
//# sourceMappingURL=supabaseAuth.d.ts.map