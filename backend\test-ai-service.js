const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:3002';

// Test data
const testProjectId = 'test-project-123';
const testUserId = 'test-user-123';

// Mock authentication token (in a real scenario, you'd get this from login)
const testAuthToken = 'test-auth-token';

async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (testAuthToken) {
    headers.Authorization = `Bearer ${testAuthToken}`;
  }

  const response = await fetch(url, {
    ...options,
    headers,
  });

  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${data.error || response.statusText}`);
  }

  return data;
}

async function testAIServiceHealth() {
  console.log('🔍 Testing AI Service Health...');
  try {
    const response = await makeRequest('/api/ai/health');
    console.log('✅ AI Service Health:', response);
    
    if (response.success) {
      console.log(`📊 OpenAI: ${response.providers.openai ? '✅' : '❌'}`);
      console.log(`📊 Anthropic: ${response.providers.anthropic ? '✅' : '❌'}`);
      console.log(`📊 Pinecone: ${response.providers.pinecone ? '✅' : '❌'}`);
      console.log(`📊 Status: ${response.status}`);
    }
    
    return response.success && (response.providers.openai || response.providers.anthropic);
  } catch (error) {
    console.error('❌ AI Service Health Test Failed:', error.message);
    return false;
  }
}

async function testCodeGeneration() {
  console.log('\n🚀 Testing Code Generation...');
  try {
    const request = {
      prompt: 'Create a simple React Native button component with TypeScript',
      projectId: testProjectId,
      language: 'typescript',
      filePath: 'src/components/CustomButton.tsx'
    };

    console.log('📤 Sending request:', request);
    
    const response = await makeRequest('/api/ai/generate', {
      method: 'POST',
      body: JSON.stringify(request),
    });

    console.log('✅ Code Generation Response:', {
      success: response.success,
      sessionId: response.sessionId,
      hasCode: !!response.generatedCode?.code,
      codeLength: response.generatedCode?.code?.length || 0,
      language: response.generatedCode?.language,
      confidence: response.generatedCode?.confidence
    });

    if (response.generatedCode?.code) {
      console.log('📝 Generated Code Preview:');
      console.log(response.generatedCode.code.substring(0, 200) + '...');
    }

    return response.success;
  } catch (error) {
    console.error('❌ Code Generation Test Failed:', error.message);
    return false;
  }
}

async function testCodeExplanation() {
  console.log('\n📖 Testing Code Explanation...');
  try {
    const sampleCode = `
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

interface ButtonProps {
  title: string;
  onPress: () => void;
}

const CustomButton: React.FC<ButtonProps> = ({ title, onPress }) => {
  return (
    <TouchableOpacity style={styles.button} onPress={onPress}>
      <Text style={styles.text}>{title}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
  },
  text: {
    color: 'white',
    textAlign: 'center',
    fontWeight: 'bold',
  },
});

export default CustomButton;
    `;

    const request = {
      code: sampleCode.trim(),
      language: 'typescript',
      projectId: testProjectId,
      filePath: 'src/components/CustomButton.tsx'
    };

    console.log('📤 Sending explanation request...');
    
    const response = await makeRequest('/api/ai/explain', {
      method: 'POST',
      body: JSON.stringify(request),
    });

    console.log('✅ Code Explanation Response:', {
      success: response.success,
      hasExplanation: !!response.explanation,
      explanationLength: response.explanation?.length || 0
    });

    if (response.explanation) {
      console.log('📖 Explanation Preview:');
      console.log(response.explanation.substring(0, 300) + '...');
    }

    return response.success;
  } catch (error) {
    console.error('❌ Code Explanation Test Failed:', error.message);
    return false;
  }
}

async function testAIChat() {
  console.log('\n💬 Testing AI Chat...');
  try {
    const request = {
      message: 'What are the best practices for React Native performance optimization?',
      projectId: testProjectId,
      includeContext: true
    };

    console.log('📤 Sending chat request...');
    
    const response = await makeRequest('/api/ai/chat', {
      method: 'POST',
      body: JSON.stringify(request),
    });

    console.log('✅ AI Chat Response:', {
      success: response.success,
      hasResponse: !!response.response,
      responseLength: response.response?.length || 0,
      hasSuggestedActions: !!response.suggestedActions?.length
    });

    if (response.response) {
      console.log('💬 Chat Response Preview:');
      console.log(response.response.substring(0, 300) + '...');
    }

    return response.success;
  } catch (error) {
    console.error('❌ AI Chat Test Failed:', error.message);
    return false;
  }
}

async function testCodeFix() {
  console.log('\n🔧 Testing Code Fix...');
  try {
    const brokenCode = `
import React from 'react';
import { View, Text } from 'react-native';

const BrokenComponent = () => {
  const [count, setCount] = useState(0); // Missing import
  
  return (
    <View>
      <Text>Count: {count}</Text>
      <Button title="Increment" onPress={() => setCount(count + 1)} /> // Missing import
    </View>
  );
};

export default BrokenComponent;
    `;

    const request = {
      code: brokenCode.trim(),
      error: 'ReferenceError: useState is not defined, Button is not defined',
      language: 'typescript',
      filePath: 'src/components/BrokenComponent.tsx',
      projectId: testProjectId
    };

    console.log('📤 Sending fix request...');
    
    const response = await makeRequest('/api/ai/fix', {
      method: 'POST',
      body: JSON.stringify(request),
    });

    console.log('✅ Code Fix Response:', {
      success: response.success,
      hasFixedCode: !!response.codeFix?.fixedCode,
      confidence: response.codeFix?.confidence
    });

    if (response.codeFix?.fixedCode) {
      console.log('🔧 Fixed Code Preview:');
      console.log(response.codeFix.fixedCode.substring(0, 300) + '...');
    }

    return response.success;
  } catch (error) {
    console.error('❌ Code Fix Test Failed:', error.message);
    return false;
  }
}

async function testServerHealth() {
  console.log('🏥 Testing Server Health...');
  try {
    const response = await makeRequest('/health');
    console.log('✅ Server Health:', response);
    return true;
  } catch (error) {
    console.error('❌ Server Health Test Failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('⏳ Waiting for server to start...');
  // Give the server time to start
  await new Promise(resolve => setTimeout(resolve, 5000));

  console.log('🧪 Starting AI Service Integration Tests...');
  console.log('🧪 Starting AI Service Integration Tests...');
  console.log('=' .repeat(50));

  const results = {
    serverHealth: false,
    aiHealth: false,
    codeGeneration: false,
    codeExplanation: false,
    aiChat: false,
    codeFix: false
  };

  // Test server health first
  results.serverHealth = await testServerHealth();
  
  if (!results.serverHealth) {
    console.log('\n❌ Server is not running. Please start the backend server first.');
    console.log('Run: npm run dev in the backend directory');
    return;
  }

  // Test AI service health
  results.aiHealth = await testAIServiceHealth();
  
  if (!results.aiHealth) {
    console.log('\n⚠️  AI Service not available. Check your API keys in .env file.');
    console.log('Make sure OPENAI_API_KEY or ANTHROPIC_API_KEY is set.');
    return;
  }

  // Run AI function tests
  results.codeGeneration = await testCodeGeneration();
  results.codeExplanation = await testCodeExplanation();
  results.aiChat = await testAIChat();
  results.codeFix = await testCodeFix();

  // Summary
  console.log('\n' + '=' .repeat(50));
  console.log('🎯 Test Results Summary:');
  console.log('=' .repeat(50));
  
  Object.entries(results).forEach(([test, passed]) => {
    const icon = passed ? '✅' : '❌';
    const testName = test
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase());
    console.log(`${icon} ${testName}`);
  });

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log('\n📊 Overall Results:');
  console.log(`${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! AI service is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Check the error messages above.');
  }
}

// Run the tests
runAllTests().catch(console.error);
