import { Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AuthRequest, JWTPayload } from '@/types';
import { logger } from '@/utils/logger';
import { AuthService } from '@/services/auth';

export const authenticateToken = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    res.status(401).json({
      error: 'Access token required'
    });
    return;
  }

  try {
    // Try Supabase auth first
    const user = await AuthService.verifyToken(token);

    if (user) {
      req.user = user;
      next();
      return;
    }

    // Fallback to JWT verification for non-Supabase auth
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;

    // Add user info to request
    req.user = {
      id: decoded.userId,
      email: decoded.email,
      name: '', // Will be populated from database if needed
      subscriptionPlan: 'free',
      apiTokens: {},
      preferences: {
        theme: 'light',
        editorSettings: {
          fontSize: 14,
          tabSize: 2,
          wordWrap: true,
          minimap: true
        },
        defaultTemplate: 'basic',
        aiProvider: 'openai'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    next();
  } catch (error) {
    logger.error('Token verification failed:', error);

    if (error instanceof jwt.TokenExpiredError) {
      res.status(401).json({
        error: 'Token expired',
        code: 'TOKEN_EXPIRED'
      });
    } else if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({
        error: 'Invalid token',
        code: 'INVALID_TOKEN'
      });
    } else {
      res.status(401).json({
        error: 'Token verification failed'
      });
    }
  }
};

export const optionalAuth = (req: AuthRequest, res: Response, next: NextFunction): void => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    // No token provided, continue without authentication
    next();
    return;
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;
    
    req.user = {
      id: decoded.userId,
      email: decoded.email,
      name: '',
      subscriptionPlan: 'free',
      apiTokens: {},
      preferences: {
        theme: 'light',
        editorSettings: {
          fontSize: 14,
          tabSize: 2,
          wordWrap: true,
          minimap: true
        },
        defaultTemplate: 'basic',
        aiProvider: 'openai'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    next();
  } catch (error) {
    // Invalid token, but continue without authentication
    logger.warn('Optional auth failed, continuing without authentication:', error);
    next();
  }
};