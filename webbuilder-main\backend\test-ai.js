// Test AI functionality
const fetch = require('node-fetch');

async function testAI() {
  console.log('🧪 Testing AI Web Builder Backend...');
  
  try {
    // Test health endpoint
    console.log('1️⃣ Testing health endpoint...');
    const healthResponse = await fetch('http://localhost:3001/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health:', healthData);
    
    // Test AI chat
    console.log('2️⃣ Testing AI chat...');
    const chatResponse = await fetch('http://localhost:3001/api/ai/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'Hello, can you help me create a simple React component?',
        projectId: 'test-123'
      })
    });
    
    const chatData = await chatResponse.json();
    console.log('✅ AI Chat Response:', chatData);
    
    // Test code generation
    console.log('3️⃣ Testing code generation...');
    const codeResponse = await fetch('http://localhost:3001/api/ai/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: 'Create a React Native button component with TypeScript',
        projectId: 'test-123',
        language: 'typescript'
      })
    });
    
    const codeData = await codeResponse.json();
    console.log('✅ Code Generation Response:', codeData);
    
    console.log('🎉 All tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAI();
