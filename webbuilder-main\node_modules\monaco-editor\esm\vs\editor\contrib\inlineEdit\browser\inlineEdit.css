/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-editor .inline-edit-remove {
	background-color: var(--vscode-editorGhostText-background);
	font-style: italic;
}

.monaco-editor .inline-edit-hidden {
	opacity: 0;
	font-size: 0;
}

.monaco-editor .inline-edit-decoration, .monaco-editor .suggest-preview-text .inline-edit {
	font-style: italic;
}

.monaco-editor .inline-completion-text-to-replace {
	text-decoration: underline;
	text-underline-position: under;
}

.monaco-editor .inline-edit-decoration,
.monaco-editor .inline-edit-decoration-preview,
.monaco-editor .suggest-preview-text .inline-edit {
	color: var(--vscode-editorGhostText-foreground) !important;
	background-color: var(--vscode-editorGhostText-background);
	border: 1px solid var(--vscode-editorGhostText-border);
}


