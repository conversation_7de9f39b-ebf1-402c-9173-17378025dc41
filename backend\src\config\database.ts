import { Pool } from 'pg';
import sqlite3 from 'sqlite3';
import { open, Database } from 'sqlite';
import { logger } from '@/utils/logger';

// Database type configuration
const DB_TYPE = process.env.DB_TYPE || 'sqlite'; // 'sqlite', 'postgresql', or 'supabase'

// Import Supabase configuration
import {
  supabase,
  initializeDatabase as initializeSupabase,
  db as supabaseDb,
  pool as supabasePool,
  DatabaseInterface as SupabaseDatabaseInterface
} from './supabase';

// SQLite database instance
let sqliteDb: Database<sqlite3.Database, sqlite3.Statement> | null = null;

// PostgreSQL pool instance
let pgPool: Pool | null = null;

// Database configuration with proper validation
const getDatabaseConfig = () => {
  // If DATABASE_URL is provided, use it (for production/cloud deployments)
  if (process.env.DATABASE_URL) {
    return {
      connectionString: process.env.DATABASE_URL,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    };
  }

  // Otherwise, use individual connection parameters
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'mobile_app_builder',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  };

  // Ensure password is a string (fix for SASL error)
  if (config.password === undefined || config.password === null) {
    config.password = '';
  }

  return config;
};

// Initialize PostgreSQL pool
if (DB_TYPE === 'postgresql') {
  pgPool = new Pool(getDatabaseConfig());
}

// PostgreSQL event handlers
if (pgPool) {
  pgPool.on('connect', () => {
    logger.info('Connected to PostgreSQL database');
  });

  pgPool.on('error', (err) => {
    logger.error('PostgreSQL connection error:', err);
    process.exit(-1);
  });
}

// Initialize SQLite database
const initializeSQLite = async (): Promise<Database<sqlite3.Database, sqlite3.Statement>> => {
  const dbPath = process.env.SQLITE_DB_PATH || './data/app.db';

  // Ensure directory exists
  const fs = require('fs');
  const path = require('path');
  const dir = path.dirname(dbPath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  const db = await open({
    filename: dbPath,
    driver: sqlite3.Database
  });

  logger.info(`SQLite database initialized at: ${dbPath}`);
  return db;
};

// Database interface for unified access
export interface DatabaseInterface {
  query(text: string, params?: any[]): Promise<any>;
  close(): Promise<void>;
}

// SQLite adapter
class SQLiteAdapter implements DatabaseInterface {
  constructor(private db: Database<sqlite3.Database, sqlite3.Statement>) {}

  async query(text: string, params: any[] = []): Promise<any> {
    // Convert PostgreSQL-style queries to SQLite
    const sqliteQuery = text
      .replace(/\$(\d+)/g, '?') // Replace $1, $2, etc. with ?
      .replace(/RETURNING \*/g, '') // Remove RETURNING clause
      .replace(/SERIAL/g, 'INTEGER PRIMARY KEY AUTOINCREMENT')
      .replace(/TIMESTAMP WITH TIME ZONE/g, 'DATETIME')
      .replace(/JSONB/g, 'TEXT')
      .replace(/TEXT\[\]/g, 'TEXT');

    if (text.includes('INSERT') && text.includes('RETURNING')) {
      // Handle INSERT with RETURNING
      const tableName = text.match(/INSERT INTO (\w+)/)?.[1];

      // Execute the INSERT without RETURNING
      await this.db.run(sqliteQuery, params);

      // Get the inserted row by ID (first parameter should be the ID)
      if (tableName && params.length > 0) {
        const row = await this.db.get(`SELECT * FROM ${tableName} WHERE id = ?`, [params[0]]);
        return { rows: row ? [row] : [] };
      }
      return { rows: [] };
    } else if (text.includes('SELECT')) {
      const rows = await this.db.all(sqliteQuery, params);
      return { rows };
    } else {
      const result = await this.db.run(sqliteQuery, params);
      return { rows: [], changes: result.changes };
    }
  }

  async close(): Promise<void> {
    await this.db.close();
  }
}

// PostgreSQL adapter
class PostgreSQLAdapter implements DatabaseInterface {
  constructor(private pool: Pool) {}

  async query(text: string, params: any[] = []): Promise<any> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(text, params);
      return result;
    } finally {
      client.release();
    }
  }

  async close(): Promise<void> {
    await this.pool.end();
  }
}

// Export unified database interface
export let db: DatabaseInterface;

// Legacy pool export for backward compatibility
export const pool = DB_TYPE === 'supabase' ? supabasePool : {
  query: async (text: string, params?: any[]) => {
    return await db.query(text, params);
  },
  connect: async () => {
    // For backward compatibility
    return {
      query: async (text: string, params?: any[]) => {
        return await db.query(text, params);
      },
      release: () => {}
    };
  }
} as any; // Type assertion to satisfy Pool interface requirements

export const initializeDatabase = async (): Promise<void> => {
  try {
    if (DB_TYPE === 'supabase') {
      logger.info('Initializing Supabase database...');
      await initializeSupabase();
      db = supabaseDb;
      logger.info('Supabase database connection established successfully');
    } else if (DB_TYPE === 'sqlite') {
      logger.info('Initializing SQLite database...');
      sqliteDb = await initializeSQLite();
      db = new SQLiteAdapter(sqliteDb);

      // Test the connection
      await db.query('SELECT 1');
      logger.info('SQLite database connection established successfully');
    } else {
      logger.info('Initializing PostgreSQL database...');
      if (!pgPool) {
        throw new Error('PostgreSQL pool not initialized');
      }
      db = new PostgreSQLAdapter(pgPool);

      // Test the connection
      await db.query('SELECT NOW()');
      logger.info('PostgreSQL database connection established successfully');
    }
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    throw error;
  }
};