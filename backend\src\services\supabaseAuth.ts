import { supabase, supabaseHelpers } from '@/config/supabase';
import { User, LoginRequest, RegisterRequest, AuthResponse, UserPreferences } from '@/types';
import { logger } from '@/utils/logger';

export class SupabaseAuthService {
  static async register(data: RegisterRequest): Promise<AuthResponse> {
    const { email, password, name } = data;

    try {
      logger.info('Registering user with Supabase Auth', { email });

      // Register user with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name
          }
        }
      });

      if (authError) {
        logger.error('Supabase auth registration error:', authError);
        throw new Error(authError.message);
      }

      if (!authData.user) {
        throw new Error('User registration failed - no user data returned');
      }

      // Create user profile
      try {
        await supabaseHelpers.createUser({
          id: authData.user.id,
          email: authData.user.email,
          name: name,
          subscription_plan: 'free',
          api_tokens: {},
          preferences: {}
        });
      } catch (profileError) {
        logger.warn('Failed to create user profile, user may already exist:', profileError);
      }

      // For registration, we might not get a session immediately if email confirmation is required
      // In that case, we'll create a temporary token for the response
      let accessToken = 'temp_token_pending_confirmation';
      let refreshToken = 'temp_refresh_token_pending_confirmation';

      if (authData.session) {
        accessToken = authData.session.access_token;
        refreshToken = authData.session.refresh_token;
      } else {
        logger.info('Registration successful but session not created (email confirmation may be required)');
      }

      const user = this.sanitizeUser({
        id: authData.user.id,
        email: authData.user.email!,
        name: name,
        avatar: null,
        subscriptionPlan: 'free',
        apiTokens: {},
        preferences: {},
        createdAt: new Date(authData.user.created_at),
        updatedAt: new Date()
      });

      return {
        user,
        accessToken,
        refreshToken
      };

    } catch (error) {
      logger.error('Registration error:', error);
      throw error;
    }
  }

  static async login(data: LoginRequest): Promise<AuthResponse> {
    const { email, password } = data;

    try {
      logger.info('Logging in user with Supabase Auth', { email });

      // Sign in with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (authError) {
        logger.error('Supabase auth login error:', authError);
        throw new Error(authError.message);
      }

      if (!authData.user || !authData.session) {
        throw new Error('Login failed - no user or session data returned');
      }

      // Get user profile
      let userProfile;
      try {
        userProfile = await supabaseHelpers.getUserById(authData.user.id);
      } catch (error) {
        logger.warn('User profile not found, creating one:', error);
        // Create profile if it doesn't exist
        userProfile = await supabaseHelpers.createUser({
          id: authData.user.id,
          email: authData.user.email,
          name: authData.user.user_metadata?.name || email.split('@')[0],
          subscription_plan: 'free',
          api_tokens: {},
          preferences: {}
        });
      }

      const user = this.sanitizeUser({
        id: authData.user.id,
        email: authData.user.email!,
        name: userProfile?.name || authData.user.user_metadata?.name || email.split('@')[0],
        avatar: userProfile?.avatar || null,
        subscriptionPlan: userProfile?.subscription_plan || 'free',
        apiTokens: userProfile?.api_tokens || {},
        preferences: userProfile?.preferences || {},
        createdAt: new Date(authData.user.created_at),
        updatedAt: new Date(userProfile?.updated_at || authData.user.updated_at)
      });

      return {
        user,
        accessToken: authData.session.access_token,
        refreshToken: authData.session.refresh_token
      };

    } catch (error) {
      logger.error('Login error:', error);
      throw error;
    }
  }

  static async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      const { data, error } = await supabase.auth.refreshSession({ refresh_token: refreshToken });

      if (error || !data.session) {
        throw new Error('Failed to refresh token');
      }

      return {
        accessToken: data.session.access_token,
        refreshToken: data.session.refresh_token
      };

    } catch (error) {
      logger.error('Token refresh error:', error);
      throw error;
    }
  }

  static async logout(userId: string, refreshToken?: string): Promise<void> {
    try {
      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        logger.error('Supabase logout error:', error);
        throw error;
      }

      logger.info('User logged out successfully', { userId });

    } catch (error) {
      logger.error('Logout error:', error);
      throw error;
    }
  }

  static async verifyToken(token: string): Promise<User | null> {
    try {
      // Verify token with Supabase
      const { data: { user }, error } = await supabase.auth.getUser(token);

      if (error || !user) {
        return null;
      }

      // Get user profile
      let userProfile;
      try {
        userProfile = await supabaseHelpers.getUserById(user.id);
      } catch (error) {
        logger.warn('User profile not found during token verification:', error);
        return null;
      }

      return this.sanitizeUser({
        id: user.id,
        email: user.email!,
        name: userProfile?.name || user.user_metadata?.name || user.email!.split('@')[0],
        avatar: userProfile?.avatar || null,
        subscriptionPlan: userProfile?.subscription_plan || 'free',
        apiTokens: userProfile?.api_tokens || {},
        preferences: userProfile?.preferences || {},
        createdAt: new Date(user.created_at),
        updatedAt: new Date(userProfile?.updated_at || user.updated_at)
      });

    } catch (error) {
      logger.error('Token verification error:', error);
      return null;
    }
  }

  static async cleanupExpiredTokens(): Promise<void> {
    // Supabase handles token cleanup automatically
    logger.info('Supabase handles token cleanup automatically');
  }

  private static sanitizeUser(user: any): User {
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      subscriptionPlan: user.subscriptionPlan,
      apiTokens: user.apiTokens,
      preferences: user.preferences,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };
  }
}
