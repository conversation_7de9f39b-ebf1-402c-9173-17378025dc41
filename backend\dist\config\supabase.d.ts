import { SupabaseClient } from '@supabase/supabase-js';
export declare let supabase: SupabaseClient;
export interface DatabaseInterface {
    query(text: string, params?: any[]): Promise<any>;
    close(): Promise<void>;
}
export declare let db: DatabaseInterface;
export declare const pool: any;
export declare const initializeDatabase: () => Promise<void>;
export declare const supabaseHelpers: {
    createUser(userData: any): Promise<any>;
    getUserById(id: string): Promise<any>;
    getUserByEmail(email: string): Promise<any>;
    createProject(projectData: any): Promise<any>;
    getProjectById(id: string): Promise<any>;
    getUserProjects(userId: string): Promise<any[]>;
    createProjectFile(fileData: any): Promise<any>;
    getProjectFiles(projectId: string): Promise<any[]>;
    updateProjectFile(id: string, updates: any): Promise<any>;
    deleteProjectFile(id: string): Promise<void>;
};
//# sourceMappingURL=supabase.d.ts.map