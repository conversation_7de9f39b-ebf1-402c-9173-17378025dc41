{"name": "mobile-app-builder-backend", "version": "1.0.0", "description": "Backend services for Mobile App Builder Platform", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node -r tsconfig-paths/register dist/server.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "@pinecone-database/pinecone": "^6.1.2", "@supabase/supabase-js": "^2.53.0", "@types/sqlite3": "^3.1.11", "bcryptjs": "^2.4.3", "chokidar": "^3.5.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "metro": "^0.80.0", "metro-config": "^0.80.0", "metro-core": "^0.80.0", "metro-file-map": "^0.80.0", "metro-resolver": "^0.80.0", "metro-transform-worker": "^0.80.0", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "openai": "^5.11.0", "pg": "^8.11.3", "socket.io": "^4.7.4", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.4", "@types/pg": "^8.10.9", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.7", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.4", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}}