{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T19:54:09.874Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T19:54:09.918Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:33:39.295Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:33:39.331Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:33:50.116Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:33:50.152Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:34:33.763Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:34:33.918Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:39:26.061Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:39:26.098Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:39:32.747Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:39:32.784Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:41:44.610Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:41:44.647Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:43:27.803Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:43:27.840Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:05.114Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:05.118Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:05.133Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.155Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.158Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.171Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:12.440Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:12.455Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:12.460Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.478Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.494Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.498Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:58:47.946Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:58:47.954Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:58:47.966Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:47.986Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:47.993Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:48.006Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:01.328Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:01.336Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.372Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:01.370Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.379Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.412Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:39.413Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:39.427Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:39.429Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.452Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.468Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.469Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T21:00:01.654Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T21:00:01.666Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.698Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T21:00:01.692Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.710Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.736Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:30:18.913Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:30:19.139Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:144:5)","timestamp":"2025-07-30T22:30:19.187Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:35:09.894Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:35:10.119Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:144:5)","timestamp":"2025-07-30T22:35:10.161Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.056Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.271Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:06.306Z"}
{"level":"warn","message":"Database connection failed in development mode. Continuing without database... SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:06.306Z"}
{"level":"warn","message":"Some features requiring database will not be available.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.306Z"}
{"level":"info","message":"Server running on port 3001","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.308Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.308Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.308Z"}
{"level":"info","message":"Health check available at: http://localhost:3001/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.308Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:11.229Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:11.453Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:11.488Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.655Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.876Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:28.913Z"}
{"level":"warn","message":"Database connection failed in development mode. Continuing without database... SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:28.913Z"}
{"level":"warn","message":"Some features requiring database will not be available.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.914Z"}
{"level":"info","message":"Server running on port 3001","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.915Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.915Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.915Z"}
{"level":"info","message":"Health check available at: http://localhost:3001/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.915Z"}
{"level":"info","message":"Received SIGINT. Starting graceful shutdown...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:48.729Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:34:14.835Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:34:15.068Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:34:15.105Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.083Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.316Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:35:07.352Z"}
{"error":"SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.353Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.353Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.354Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.354Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.355Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.355Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:35:24.417Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:35:24 +0000] \"GET /health HTTP/1.1\" 503 199 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4768\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:24.419Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:35:28.946Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:35:28 +0000] \"GET /health HTTP/1.1\" 503 199 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4768\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:28.946Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.621Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.863Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:35:54.898Z"}
{"error":"SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.899Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.899Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.900Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.900Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.901Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.901Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.666Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.900Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:38:15.937Z"}
{"error":"SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.938Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.938Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.939Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.939Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.940Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.940Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:35.831Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.068Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:38:36.104Z"}
{"error":"SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.104Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.105Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.106Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.106Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.106Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.106Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:40:28.694Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:40:28 +0000] \"GET /health HTTP/1.1\" 503 200 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:28.696Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:40:28 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:28.698Z"}
{"level":"warn","message":"Database not available, using in-memory auth storage","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:28.873Z"}
{"level":"info","message":"User registered successfully (in-memory): <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:29.055Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:40:29 +0000] \"POST /api/auth/register HTTP/1.1\" 201 920 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:29.056Z"}
{"level":"warn","message":"Database not available, using in-memory auth storage","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:29.082Z"}
{"level":"info","message":"User logged in successfully (in-memory): <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:29.258Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:40:29 +0000] \"POST /api/auth/login HTTP/1.1\" 200 908 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:29.259Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.683Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.920Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.920Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.922Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.923Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.923Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.923Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:48:49.040Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:48:49.041Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:48:49.041Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:48:49.041Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:48:49.042Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:48:49.042Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.042Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.042Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.043Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.043Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.043Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.043Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.156Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.390Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.391Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.392Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.393Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.393Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.393Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:49:13.396Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:49:13.396Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:49:13.397Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:49:13.397Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:49:13.398Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:49:13.398Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.398Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.398Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.399Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.399Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.399Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.400Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:49:53 +0000] \"GET /health HTTP/1.1\" 200 194 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:53.616Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:49:53 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:53.620Z"}
{"level":"error","message":"Registration error: Cannot read properties of undefined (reading 'id')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'id')\n    at Function.registerWithDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:76:21)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:31:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-30T23:49:53.828Z"}
{"level":"error","message":"Registration endpoint error: Cannot read properties of undefined (reading 'id')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'id')\n    at Function.registerWithDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:76:21)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:31:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-30T23:49:53.829Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:49:53 +0000] \"POST /api/auth/register HTTP/1.1\" 500 69 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:53.829Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:23.824Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.054Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.054Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.056Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.057Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.057Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.058Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:50:24.061Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:24.061Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:50:24.062Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:24.062Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:50:24.062Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:24.063Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.063Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.063Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.065Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.065Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.065Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.065Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.547Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.776Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.776Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.777Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.778Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.779Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.779Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:50:45.782Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:45.782Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:50:45.783Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:45.783Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:50:45.783Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:45.783Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.784Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.784Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.785Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.785Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.785Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.785Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:51:04 +0000] \"GET /health HTTP/1.1\" 200 194 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:04.147Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:51:04 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:04.150Z"}
{"code":"SQLITE_RANGE","errno":25,"level":"error","message":"Registration error: SQLITE_RANGE: column index out of range","service":"mobile-app-builder-backend","stack":"Error: SQLITE_RANGE: column index out of range","timestamp":"2025-07-30T23:51:04.397Z"}
{"code":"SQLITE_RANGE","errno":25,"level":"error","message":"Registration endpoint error: SQLITE_RANGE: column index out of range","service":"mobile-app-builder-backend","stack":"Error: SQLITE_RANGE: column index out of range","timestamp":"2025-07-30T23:51:04.397Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:51:04 +0000] \"POST /api/auth/register HTTP/1.1\" 500 69 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:04.398Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.547Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.792Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.793Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.794Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.795Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.795Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.795Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:51:39.798Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:39.799Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:51:39.799Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:39.800Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:51:39.800Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:39.800Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.800Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.800Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.802Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.802Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.802Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.802Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:54.964Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.199Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.199Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.201Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.202Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.203Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.203Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:51:55.206Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:55.206Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:51:55.206Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:55.207Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:51:55.207Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:55.207Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.207Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.207Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.208Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.208Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.208Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.209Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:13.854Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.109Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.110Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.112Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.114Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.114Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.114Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:52:14.117Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:14.118Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:52:14.119Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:14.119Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:52:14.119Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:14.119Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.120Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.120Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.121Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.121Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.121Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.121Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.258Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.543Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.544Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.545Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.547Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.547Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.547Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:52:35.551Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:35.551Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:52:35.551Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:35.552Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:52:35.552Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:35.552Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.553Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.553Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.554Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.555Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.555Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.555Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:52:53 +0000] \"GET /health HTTP/1.1\" 200 194 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:53.682Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:52:53 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:53.685Z"}
{"level":"info","message":"User registered successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:53.936Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:52:53 +0000] \"POST /api/auth/register HTTP/1.1\" 201 965 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:53.937Z"}
{"level":"info","message":"User logged in successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:54.125Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:52:54 +0000] \"POST /api/auth/login HTTP/1.1\" 200 953 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:54.126Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.263Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.550Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.551Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.553Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.554Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.555Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.555Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:54:22.558Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:54:22.559Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:54:22.559Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:54:22.560Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:54:22.560Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:54:22.560Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.561Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.561Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.563Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.563Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.563Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.563Z"}
{"level":"info","message":"User registered successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:55:27.820Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:55:27 +0000] \"POST /api/auth/register HTTP/1.1\" 201 979 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:55:27.823Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:58:12 +0000] \"POST /api/ai/generate HTTP/1.1\" - - \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:58:12.368Z"}
{"level":"info","message":"User registered successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:58:41.591Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:58:41 +0000] \"POST /api/auth/register HTTP/1.1\" 201 999 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:58:41.592Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:59:48 +0000] \"POST /api/ai/chat HTTP/1.1\" - - \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:59:48.664Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.456Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.749Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.750Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.751Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.753Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.753Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.754Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-31T00:00:35.757Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:35.758Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-31T00:00:35.758Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:35.759Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-31T00:00:35.759Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:35.759Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.759Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.759Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.761Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.761Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.761Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.761Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.690Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.982Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.983Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.984Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.985Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.986Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.986Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-31T00:00:48.989Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:48.990Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-31T00:00:48.990Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:48.991Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-31T00:00:48.991Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:48.991Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.991Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.991Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.993Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.993Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.993Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.993Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.271Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.598Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.599Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.602Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.604Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.604Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.605Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-31T00:01:06.608Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:06.608Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-31T00:01:06.609Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:06.609Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-31T00:01:06.609Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:06.610Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.610Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.610Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.612Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.612Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.612Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.612Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.247Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.559Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.560Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.561Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.563Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.563Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.564Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-31T00:01:18.568Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:18.568Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-31T00:01:18.568Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:18.569Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-31T00:01:18.569Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:18.569Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.570Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.570Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.571Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.571Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.571Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.571Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:41.964Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.241Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.241Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.244Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.245Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.246Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.246Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-31T00:01:42.250Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:42.251Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-31T00:01:42.251Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:42.251Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-31T00:01:42.252Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:42.252Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.252Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.252Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.254Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.254Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.254Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.254Z"}
{"level":"info","message":"User registered successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:02:01.124Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:02:01 +0000] \"POST /api/auth/register HTTP/1.1\" 201 999 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:02:01.127Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:03:56 +0000] \"POST /api/ai/chat HTTP/1.1\" - - \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:03:56.959Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:04:30.177Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:04:30.179Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:04:30.182Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:04:30.182Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:05:30 +0000] \"GET /health HTTP/1.1\" 200 195 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:30.918Z"}
{"level":"info","message":"User registered successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:31.116Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:05:31 +0000] \"POST /api/auth/register HTTP/1.1\" 201 993 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:31.116Z"}
{"level":"info","message":"User logged in successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:31.302Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:05:31 +0000] \"POST /api/auth/login HTTP/1.1\" 200 981 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:31.303Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:05:31 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:31.304Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.059Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.290Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.291Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.292Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.293Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.293Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.293Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-31T00:11:56.297Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:11:56.297Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-31T00:11:56.297Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:11:56.298Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-31T00:11:56.298Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:11:56.298Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.298Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.298Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.300Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.300Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.300Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:11:56.300Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.491Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.720Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.720Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.722Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.723Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.724Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.724Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:12:29.726Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:12:29.727Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:12:29.727Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.727Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.727Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.729Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.729Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.729Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:29.729Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.615Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.843Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.843Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.844Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.846Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.846Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.846Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:12:58.848Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at AFTER UPDATE ON projects FOR EACH ROW BEGIN ","timestamp":"2025-07-31T00:12:58.849Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:12:58.849Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at AFTER UPDATE ON ai_sessions FOR EACH ROW ","timestamp":"2025-07-31T00:12:58.849Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:12:58.850Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.850Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.850Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.851Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.851Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.851Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:12:58.851Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:18.781Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.009Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.009Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.011Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.012Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.012Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.012Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:13:19.015Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at AFTER UPDATE ON projects FOR EACH ROW BEGIN ","timestamp":"2025-07-31T00:13:19.015Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:13:19.015Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at AFTER UPDATE ON ai_sessions FOR EACH ROW ","timestamp":"2025-07-31T00:13:19.015Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:13:19.016Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.016Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.016Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.018Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.018Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.018Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:13:19.018Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.216Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.458Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.459Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.460Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.461Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.461Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.462Z"}
{"error":"SQLITE_ERROR: no such table: main.projects","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)","timestamp":"2025-07-31T00:14:06.463Z"}
{"error":"SQLITE_ERROR: no such table: main.project_files","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id)","timestamp":"2025-07-31T00:14:06.464Z"}
{"error":"SQLITE_ERROR: no such table: main.project_files","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_path ON project_files(project_id, path)","timestamp":"2025-07-31T00:14:06.464Z"}
{"error":"SQLITE_ERROR: no such table: main.builds","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_project_id ON builds(project_id)","timestamp":"2025-07-31T00:14:06.464Z"}
{"error":"SQLITE_ERROR: no such table: main.builds","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_status ON builds(status)","timestamp":"2025-07-31T00:14:06.465Z"}
{"error":"SQLITE_ERROR: no such table: main.ai_sessions","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_project_id ON ai_sessions(project_id)","timestamp":"2025-07-31T00:14:06.465Z"}
{"error":"SQLITE_ERROR: no such table: main.ai_sessions","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_id ON ai_sessions(user_id)","timestamp":"2025-07-31T00:14:06.465Z"}
{"error":"SQLITE_ERROR: no such table: main.project_collaborators","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_project_id ON project_collaborators(project_id)","timestamp":"2025-07-31T00:14:06.465Z"}
{"error":"SQLITE_ERROR: no such table: main.project_collaborators","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_user_id ON project_collaborators(user_id)","timestamp":"2025-07-31T00:14:06.466Z"}
{"error":"SQLITE_ERROR: no such table: main.refresh_tokens","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id)","timestamp":"2025-07-31T00:14:06.466Z"}
{"error":"SQLITE_ERROR: no such table: main.refresh_tokens","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens(token)","timestamp":"2025-07-31T00:14:06.466Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.466Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.467Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.468Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.468Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.468Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:06.468Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:14:23 +0000] \"GET /health HTTP/1.1\" 200 194 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:23.120Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:14:23 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:23.123Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Registration error: SQLITE_ERROR: no such table: users","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: no such table: users","timestamp":"2025-07-31T00:14:23.133Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Registration endpoint error: SQLITE_ERROR: no such table: users","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: no such table: users","timestamp":"2025-07-31T00:14:23.133Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:14:23 +0000] \"POST /api/auth/register HTTP/1.1\" 500 69 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:14:23.134Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.527Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.762Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.762Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.765Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.766Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.767Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.767Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)","timestamp":"2025-07-31T00:15:14.767Z"}
{"error":"SQLITE_ERROR: no such table: main.projects","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)","timestamp":"2025-07-31T00:15:14.768Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id)","timestamp":"2025-07-31T00:15:14.768Z"}
{"error":"SQLITE_ERROR: no such table: main.project_files","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id)","timestamp":"2025-07-31T00:15:14.768Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_path ON project_files(project_id, path)","timestamp":"2025-07-31T00:15:14.768Z"}
{"error":"SQLITE_ERROR: no such table: main.project_files","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_path ON project_files(project_id, path)","timestamp":"2025-07-31T00:15:14.769Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_project_id ON builds(project_id)","timestamp":"2025-07-31T00:15:14.769Z"}
{"error":"SQLITE_ERROR: no such table: main.builds","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_project_id ON builds(project_id)","timestamp":"2025-07-31T00:15:14.769Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_status ON builds(status)","timestamp":"2025-07-31T00:15:14.769Z"}
{"error":"SQLITE_ERROR: no such table: main.builds","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_status ON builds(status)","timestamp":"2025-07-31T00:15:14.769Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_project_id ON ai_sessions(project_id)","timestamp":"2025-07-31T00:15:14.769Z"}
{"error":"SQLITE_ERROR: no such table: main.ai_sessions","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_project_id ON ai_sessions(project_id)","timestamp":"2025-07-31T00:15:14.769Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_id ON ai_sessions(user_id)","timestamp":"2025-07-31T00:15:14.770Z"}
{"error":"SQLITE_ERROR: no such table: main.ai_sessions","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_id ON ai_sessions(user_id)","timestamp":"2025-07-31T00:15:14.770Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_project_id ON project_collaborators(project_id)","timestamp":"2025-07-31T00:15:14.770Z"}
{"error":"SQLITE_ERROR: no such table: main.project_collaborators","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_project_id ON project_collaborators(project_id)","timestamp":"2025-07-31T00:15:14.770Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_user_id ON project_collaborators(user_id)","timestamp":"2025-07-31T00:15:14.770Z"}
{"error":"SQLITE_ERROR: no such table: main.project_collaborators","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_user_id ON project_collaborators(user_id)","timestamp":"2025-07-31T00:15:14.770Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id)","timestamp":"2025-07-31T00:15:14.770Z"}
{"error":"SQLITE_ERROR: no such table: main.refresh_tokens","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id)","timestamp":"2025-07-31T00:15:14.771Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens(token)","timestamp":"2025-07-31T00:15:14.771Z"}
{"error":"SQLITE_ERROR: no such table: main.refresh_tokens","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens(token)","timestamp":"2025-07-31T00:15:14.771Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.771Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.771Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.772Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.772Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.772Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:14.772Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:47.967Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.240Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.241Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.244Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.246Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.246Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.246Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)","timestamp":"2025-07-31T00:15:48.247Z"}
{"error":"SQLITE_ERROR: no such table: main.projects","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)","timestamp":"2025-07-31T00:15:48.248Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id)","timestamp":"2025-07-31T00:15:48.248Z"}
{"error":"SQLITE_ERROR: no such table: main.project_files","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id)","timestamp":"2025-07-31T00:15:48.249Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_path ON project_files(project_id, path)","timestamp":"2025-07-31T00:15:48.249Z"}
{"error":"SQLITE_ERROR: no such table: main.project_files","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_path ON project_files(project_id, path)","timestamp":"2025-07-31T00:15:48.250Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_project_id ON builds(project_id)","timestamp":"2025-07-31T00:15:48.250Z"}
{"error":"SQLITE_ERROR: no such table: main.builds","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_project_id ON builds(project_id)","timestamp":"2025-07-31T00:15:48.250Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_status ON builds(status)","timestamp":"2025-07-31T00:15:48.250Z"}
{"error":"SQLITE_ERROR: no such table: main.builds","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_status ON builds(status)","timestamp":"2025-07-31T00:15:48.251Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_project_id ON ai_sessions(project_id)","timestamp":"2025-07-31T00:15:48.251Z"}
{"error":"SQLITE_ERROR: no such table: main.ai_sessions","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_project_id ON ai_sessions(project_id)","timestamp":"2025-07-31T00:15:48.251Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_id ON ai_sessions(user_id)","timestamp":"2025-07-31T00:15:48.251Z"}
{"error":"SQLITE_ERROR: no such table: main.ai_sessions","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_id ON ai_sessions(user_id)","timestamp":"2025-07-31T00:15:48.251Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_project_id ON project_collaborators(project_id)","timestamp":"2025-07-31T00:15:48.251Z"}
{"error":"SQLITE_ERROR: no such table: main.project_collaborators","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_project_id ON project_collaborators(project_id)","timestamp":"2025-07-31T00:15:48.252Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_user_id ON project_collaborators(user_id)","timestamp":"2025-07-31T00:15:48.252Z"}
{"error":"SQLITE_ERROR: no such table: main.project_collaborators","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_user_id ON project_collaborators(user_id)","timestamp":"2025-07-31T00:15:48.252Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id)","timestamp":"2025-07-31T00:15:48.252Z"}
{"error":"SQLITE_ERROR: no such table: main.refresh_tokens","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id)","timestamp":"2025-07-31T00:15:48.252Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens(token)","timestamp":"2025-07-31T00:15:48.252Z"}
{"error":"SQLITE_ERROR: no such table: main.refresh_tokens","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens(token)","timestamp":"2025-07-31T00:15:48.252Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.252Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.252Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.254Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.254Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.254Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:15:48.254Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.317Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.552Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.552Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.554Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.555Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.555Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.556Z"}
{"level":"info","message":"Found 11 migration statements","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.556Z"}
{"level":"info","message":"Statement 1: CREATE INDEX IF NOT EXISTS idx_projects_user_id ON...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.556Z"}
{"level":"info","message":"Statement 2: CREATE INDEX IF NOT EXISTS idx_project_files_proje...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.556Z"}
{"level":"info","message":"Statement 3: CREATE INDEX IF NOT EXISTS idx_project_files_path ...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.557Z"}
{"level":"info","message":"Statement 4: CREATE INDEX IF NOT EXISTS idx_builds_project_id O...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.557Z"}
{"level":"info","message":"Statement 5: CREATE INDEX IF NOT EXISTS idx_builds_status ON bu...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.557Z"}
{"level":"info","message":"Statement 6: CREATE INDEX IF NOT EXISTS idx_ai_sessions_project...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.557Z"}
{"level":"info","message":"Statement 7: CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_id...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.557Z"}
{"level":"info","message":"Statement 8: CREATE INDEX IF NOT EXISTS idx_project_collaborato...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.557Z"}
{"level":"info","message":"Statement 9: CREATE INDEX IF NOT EXISTS idx_project_collaborato...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.557Z"}
{"level":"info","message":"Statement 10: CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.557Z"}
{"level":"info","message":"Statement 11: CREATE INDEX IF NOT EXISTS idx_refresh_tokens_toke...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.557Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)","timestamp":"2025-07-31T00:16:19.557Z"}
{"error":"SQLITE_ERROR: no such table: main.projects","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)","timestamp":"2025-07-31T00:16:19.558Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id)","timestamp":"2025-07-31T00:16:19.558Z"}
{"error":"SQLITE_ERROR: no such table: main.project_files","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id)","timestamp":"2025-07-31T00:16:19.558Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_path ON project_files(project_id, path)","timestamp":"2025-07-31T00:16:19.558Z"}
{"error":"SQLITE_ERROR: no such table: main.project_files","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_path ON project_files(project_id, path)","timestamp":"2025-07-31T00:16:19.559Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_project_id ON builds(project_id)","timestamp":"2025-07-31T00:16:19.559Z"}
{"error":"SQLITE_ERROR: no such table: main.builds","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_project_id ON builds(project_id)","timestamp":"2025-07-31T00:16:19.559Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_status ON builds(status)","timestamp":"2025-07-31T00:16:19.559Z"}
{"error":"SQLITE_ERROR: no such table: main.builds","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_status ON builds(status)","timestamp":"2025-07-31T00:16:19.559Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_project_id ON ai_sessions(project_id)","timestamp":"2025-07-31T00:16:19.560Z"}
{"error":"SQLITE_ERROR: no such table: main.ai_sessions","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_project_id ON ai_sessions(project_id)","timestamp":"2025-07-31T00:16:19.560Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_id ON ai_sessions(user_id)","timestamp":"2025-07-31T00:16:19.560Z"}
{"error":"SQLITE_ERROR: no such table: main.ai_sessions","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_id ON ai_sessions(user_id)","timestamp":"2025-07-31T00:16:19.560Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_project_id ON project_collaborators(project_id)","timestamp":"2025-07-31T00:16:19.560Z"}
{"error":"SQLITE_ERROR: no such table: main.project_collaborators","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_project_id ON project_collaborators(project_id)","timestamp":"2025-07-31T00:16:19.560Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_user_id ON project_collaborators(user_id)","timestamp":"2025-07-31T00:16:19.561Z"}
{"error":"SQLITE_ERROR: no such table: main.project_collaborators","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_user_id ON project_collaborators(user_id)","timestamp":"2025-07-31T00:16:19.561Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id)","timestamp":"2025-07-31T00:16:19.561Z"}
{"error":"SQLITE_ERROR: no such table: main.refresh_tokens","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id)","timestamp":"2025-07-31T00:16:19.561Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens(token)","timestamp":"2025-07-31T00:16:19.561Z"}
{"error":"SQLITE_ERROR: no such table: main.refresh_tokens","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens(token)","timestamp":"2025-07-31T00:16:19.561Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.561Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.561Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.562Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.563Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.563Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:19.563Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.172Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.411Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.411Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.413Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.414Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.414Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.415Z"}
{"level":"info","message":"Found 11 migration statements","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.416Z"}
{"level":"info","message":"Statement 1: CREATE INDEX IF NOT EXISTS idx_projects_user_id ON...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.416Z"}
{"level":"info","message":"Statement 2: CREATE INDEX IF NOT EXISTS idx_project_files_proje...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.416Z"}
{"level":"info","message":"Statement 3: CREATE INDEX IF NOT EXISTS idx_project_files_path ...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.416Z"}
{"level":"info","message":"Statement 4: CREATE INDEX IF NOT EXISTS idx_builds_project_id O...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.416Z"}
{"level":"info","message":"Statement 5: CREATE INDEX IF NOT EXISTS idx_builds_status ON bu...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.416Z"}
{"level":"info","message":"Statement 6: CREATE INDEX IF NOT EXISTS idx_ai_sessions_project...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.417Z"}
{"level":"info","message":"Statement 7: CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_id...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.417Z"}
{"level":"info","message":"Statement 8: CREATE INDEX IF NOT EXISTS idx_project_collaborato...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.417Z"}
{"level":"info","message":"Statement 9: CREATE INDEX IF NOT EXISTS idx_project_collaborato...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.417Z"}
{"level":"info","message":"Statement 10: CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.417Z"}
{"level":"info","message":"Statement 11: CREATE INDEX IF NOT EXISTS idx_refresh_tokens_toke...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.417Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)","timestamp":"2025-07-31T00:16:45.417Z"}
{"error":"SQLITE_ERROR: no such table: main.projects","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)","timestamp":"2025-07-31T00:16:45.418Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id)","timestamp":"2025-07-31T00:16:45.418Z"}
{"error":"SQLITE_ERROR: no such table: main.project_files","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_project_id ON project_files(project_id)","timestamp":"2025-07-31T00:16:45.418Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_path ON project_files(project_id, path)","timestamp":"2025-07-31T00:16:45.418Z"}
{"error":"SQLITE_ERROR: no such table: main.project_files","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_files_path ON project_files(project_id, path)","timestamp":"2025-07-31T00:16:45.418Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_project_id ON builds(project_id)","timestamp":"2025-07-31T00:16:45.418Z"}
{"error":"SQLITE_ERROR: no such table: main.builds","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_project_id ON builds(project_id)","timestamp":"2025-07-31T00:16:45.419Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_status ON builds(status)","timestamp":"2025-07-31T00:16:45.419Z"}
{"error":"SQLITE_ERROR: no such table: main.builds","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_builds_status ON builds(status)","timestamp":"2025-07-31T00:16:45.419Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_project_id ON ai_sessions(project_id)","timestamp":"2025-07-31T00:16:45.419Z"}
{"error":"SQLITE_ERROR: no such table: main.ai_sessions","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_project_id ON ai_sessions(project_id)","timestamp":"2025-07-31T00:16:45.419Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_id ON ai_sessions(user_id)","timestamp":"2025-07-31T00:16:45.419Z"}
{"error":"SQLITE_ERROR: no such table: main.ai_sessions","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_ai_sessions_user_id ON ai_sessions(user_id)","timestamp":"2025-07-31T00:16:45.419Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_project_id ON project_collaborators(project_id)","timestamp":"2025-07-31T00:16:45.419Z"}
{"error":"SQLITE_ERROR: no such table: main.project_collaborators","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_project_id ON project_collaborators(project_id)","timestamp":"2025-07-31T00:16:45.420Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_user_id ON project_collaborators(user_id)","timestamp":"2025-07-31T00:16:45.420Z"}
{"error":"SQLITE_ERROR: no such table: main.project_collaborators","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_project_collaborators_user_id ON project_collaborators(user_id)","timestamp":"2025-07-31T00:16:45.420Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id)","timestamp":"2025-07-31T00:16:45.420Z"}
{"error":"SQLITE_ERROR: no such table: main.refresh_tokens","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id)","timestamp":"2025-07-31T00:16:45.420Z"}
{"level":"info","message":"Executing migration statement:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens(token)","timestamp":"2025-07-31T00:16:45.421Z"}
{"error":"SQLITE_ERROR: no such table: main.refresh_tokens","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token ON refresh_tokens(token)","timestamp":"2025-07-31T00:16:45.421Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.421Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.421Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.422Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.422Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.422Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:16:45.422Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.151Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.435Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.435Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.437Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.438Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.438Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.438Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:19:11.439Z"}
{"error":"SQLITE_ERROR: near \"EXTENSION\": syntax error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.439Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.439Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.441Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.441Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.441Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:19:11.441Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:26.726Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.024Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.025Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.027Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.029Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.029Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.029Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:25:27.031Z"}
{"error":"SQLITE_ERROR: near \"EXTENSION\": syntax error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.031Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.031Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.033Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.034Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.034Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.034Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:26.735Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.034Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.035Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.037Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.038Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.038Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:27.039Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.657Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.955Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.956Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.957Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.959Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.959Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.960Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:25:43.961Z"}
{"error":"SQLITE_ERROR: near \"EXTENSION\": syntax error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.962Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.962Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.964Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.964Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.964Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:43.964Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:45.427Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:45.707Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:45.708Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:45.710Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:45.711Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:45.712Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:25:45.712Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.355Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.649Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.650Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.651Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.653Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.653Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.653Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:26:16.654Z"}
{"error":"SQLITE_ERROR: near \"EXTENSION\": syntax error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.654Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.654Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.656Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.656Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.656Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:16.656Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:51.716Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.045Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.045Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.047Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.049Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.050Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.050Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:26:52.051Z"}
{"error":"SQLITE_ERROR: near \"EXTENSION\": syntax error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.052Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.053Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.056Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.056Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.056Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:26:52.056Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.106Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.363Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.363Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.364Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.366Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.366Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.366Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:27:10.367Z"}
{"error":"SQLITE_ERROR: near \"EXTENSION\": syntax error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.368Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.368Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.369Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.369Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.370Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:10.370Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:39.121Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:39.123Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:39.361Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:39.361Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:39.361Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:39.998Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:39.998Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:39.999Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:39.999Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:40.000Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:40.000Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:40.001Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:40.001Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:54.558Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:54.559Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:54.812Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:54.812Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:54.812Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:55.037Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:55.037Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:55.037Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:55.037Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:55.039Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:55.039Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:55.039Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:55.039Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.353Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.354Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.586Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.587Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.587Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.826Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.827Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.827Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.827Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.828Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.829Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.829Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.829Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.221Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.223Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.499Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.499Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.500Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.708Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.708Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.709Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.709Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.710Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.711Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.711Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.711Z"}
{"level":"error","message":"Database health check failed: Cannot read properties of undefined (reading 'query')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'query')\n    at Object.query (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\supabase.ts:41:21)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:18\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-31T00:28:54.898Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:28:54 +0000] \"GET /health HTTP/1.1\" 503 198 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:54.901Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:28:54 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:54.905Z"}
{"email":"<EMAIL>","level":"info","message":"Registering user with Supabase Auth","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:54.916Z"}
{"__isAuthError":true,"code":"email_address_invalid","level":"error","message":"Supabase auth registration error: Email address \"<EMAIL>\" is invalid","name":"AuthApiError","service":"mobile-app-builder-backend","stack":"AuthApiError: Email address \"<EMAIL>\" is invalid\n    at handleError (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:102:9)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async _handleRequest (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:195:5)\n    at async _request (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:157:16)\n    at async SupabaseAuthClient.signUp (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\GoTrueClient.ts:502:15)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:13:52)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","status":400,"timestamp":"2025-07-31T00:28:55.392Z"}
{"level":"error","message":"Registration error: Email address \"<EMAIL>\" is invalid","service":"mobile-app-builder-backend","stack":"Error: Email address \"<EMAIL>\" is invalid\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:25:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:28:55.392Z"}
{"level":"error","message":"Registration error: Email address \"<EMAIL>\" is invalid","service":"mobile-app-builder-backend","stack":"Error: Email address \"<EMAIL>\" is invalid\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:25:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:28:55.393Z"}
{"level":"error","message":"Registration endpoint error: Email address \"<EMAIL>\" is invalid","service":"mobile-app-builder-backend","stack":"Error: Email address \"<EMAIL>\" is invalid\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:25:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:28:55.393Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:28:55 +0000] \"POST /api/auth/register HTTP/1.1\" 500 69 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:55.394Z"}
{"level":"error","message":"Database health check failed: Cannot read properties of undefined (reading 'query')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'query')\n    at Object.query (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\supabase.ts:41:21)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:18\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-31T00:29:30.793Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:29:30 +0000] \"GET /health HTTP/1.1\" 503 198 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:29:30.794Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:29:30 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:29:30.797Z"}
{"email":"<EMAIL>","level":"info","message":"Registering user with Supabase Auth","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:29:30.799Z"}
{"level":"warn","message":"Failed to create user profile, user may already exist:","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:29:31.720Z"}
{"level":"error","message":"Failed to get session after registration:","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:29:31.720Z"}
{"level":"error","message":"Registration error: Failed to create session","service":"mobile-app-builder-backend","stack":"Error: Failed to create session\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:51:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:29:31.720Z"}
{"level":"error","message":"Registration error: Failed to create session","service":"mobile-app-builder-backend","stack":"Error: Failed to create session\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:51:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:29:31.721Z"}
{"level":"error","message":"Registration endpoint error: Failed to create session","service":"mobile-app-builder-backend","stack":"Error: Failed to create session\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:51:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:29:31.721Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:29:31 +0000] \"POST /api/auth/register HTTP/1.1\" 500 69 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:29:31.722Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.109Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.112Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.391Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.392Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.392Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.644Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.645Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.645Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.645Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.647Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.647Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.647Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.647Z"}
{"level":"error","message":"Database health check failed: Cannot read properties of undefined (reading 'query')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'query')\n    at Object.query (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\supabase.ts:41:21)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:18\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-31T00:30:08.035Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:30:08 +0000] \"GET /health HTTP/1.1\" 503 197 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:08.038Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:30:08 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:08.041Z"}
{"email":"<EMAIL>","level":"info","message":"Registering user with Supabase Auth","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:08.052Z"}
{"level":"warn","message":"Failed to create user profile, user may already exist:","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:08.636Z"}
{"level":"info","message":"Registration successful but session not created (email confirmation may be required)","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:08.636Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:30:08 +0000] \"POST /api/auth/register HTTP/1.1\" 201 422 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:08.637Z"}
{"email":"<EMAIL>","level":"info","message":"Logging in user with Supabase Auth","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:08.639Z"}
{"__isAuthError":true,"code":"email_not_confirmed","level":"error","message":"Supabase auth login error: Email not confirmed","name":"AuthApiError","service":"mobile-app-builder-backend","stack":"AuthApiError: Email not confirmed\n    at handleError (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:102:9)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async _handleRequest (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:195:5)\n    at async _request (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:157:16)\n    at async SupabaseAuthClient.signInWithPassword (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\GoTrueClient.ts:573:15)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:89:52)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","status":400,"timestamp":"2025-07-31T00:30:08.822Z"}
{"level":"error","message":"Login error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:08.822Z"}
{"level":"error","message":"Login error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:08.822Z"}
{"level":"error","message":"Login endpoint error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:08.822Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:30:08 +0000] \"POST /api/auth/login HTTP/1.1\" 500 69 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:08.823Z"}
{"level":"error","message":"Database health check failed: Cannot read properties of undefined (reading 'query')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'query')\n    at Object.query (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\supabase.ts:41:21)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:18\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-31T00:30:39.731Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:30:39 +0000] \"GET /health HTTP/1.1\" 503 198 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:39.732Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:30:39 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:39.736Z"}
{"email":"<EMAIL>","level":"info","message":"Registering user with Supabase Auth","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:39.739Z"}
{"level":"warn","message":"Failed to create user profile, user may already exist:","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:40.330Z"}
{"level":"info","message":"Registration successful but session not created (email confirmation may be required)","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:40.331Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:30:40 +0000] \"POST /api/auth/register HTTP/1.1\" 201 422 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:40.332Z"}
{"email":"<EMAIL>","level":"info","message":"Logging in user with Supabase Auth","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:40.334Z"}
{"__isAuthError":true,"code":"email_not_confirmed","level":"error","message":"Supabase auth login error: Email not confirmed","name":"AuthApiError","service":"mobile-app-builder-backend","stack":"AuthApiError: Email not confirmed\n    at handleError (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:102:9)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async _handleRequest (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:195:5)\n    at async _request (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:157:16)\n    at async SupabaseAuthClient.signInWithPassword (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\GoTrueClient.ts:573:15)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:89:52)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","status":400,"timestamp":"2025-07-31T00:30:40.502Z"}
{"level":"error","message":"Login error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:40.502Z"}
{"level":"error","message":"Login error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:40.502Z"}
{"level":"error","message":"Login endpoint error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:40.503Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:30:40 +0000] \"POST /api/auth/login HTTP/1.1\" 500 69 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:40.504Z"}
{"level":"error","message":"Database health check failed: Cannot read properties of undefined (reading 'query')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'query')\n    at Object.query (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\supabase.ts:41:21)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:18\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-31T00:40:31.366Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:40:31 +0000] \"GET /health HTTP/1.1\" 503 199 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:31.367Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:58.936Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:58.938Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:59.170Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:59.170Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:59.170Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:59.703Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:59.703Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:59.704Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:59.704Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:59.705Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:59.706Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:59.706Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:40:59.706Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:09.823Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:09.825Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:10.056Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:10.056Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:10.057Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:10.298Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:10.299Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:10.299Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:10.299Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:10.300Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:10.301Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:10.301Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:10.301Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.120Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.122Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.349Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.349Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.349Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.587Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.588Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.588Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.589Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.590Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.590Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.590Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:21.590Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.190Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.192Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.419Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.420Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.420Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.739Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.740Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.740Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.740Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.741Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.742Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.742Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:41:46.742Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:42:09 +0000] \"GET /health HTTP/1.1\" 200 194 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:09.136Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:42:09 +0000] \"GET /api/ai/health HTTP/1.1\" 401 33 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:09.141Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:42:09 +0000] \"POST /api/ai/chat HTTP/1.1\" 401 33 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:09.156Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:42:09 +0000] \"POST /api/ai/generate HTTP/1.1\" 401 33 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:09.159Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:48.900Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:48.903Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:49.133Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:49.134Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:49.134Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:49.455Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:49.455Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:49.456Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:49.456Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:49.457Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:49.458Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:49.458Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:42:49.458Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:01.998Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.000Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.235Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.235Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.235Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.456Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.457Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.457Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.457Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.458Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.459Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.459Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:02.459Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:17.887Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:17.890Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:18.121Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:18.121Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:18.122Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:18.328Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:18.328Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:18.328Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:18.328Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:18.330Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:18.330Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:18.330Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:18.330Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.368Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.371Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.601Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.601Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.601Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.885Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.885Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.885Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.886Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.887Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.887Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.887Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:28.887Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.444Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.446Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.677Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.678Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.678Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.882Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.883Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.883Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.883Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.884Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.885Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.885Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:43:45.885Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.188Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.191Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.424Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.424Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.425Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.677Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.677Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.677Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.678Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.679Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.679Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.679Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:15.679Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:23.987Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:23.989Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:24.219Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:24.219Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:24.219Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:24.447Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:24.448Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:24.448Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:24.449Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:24.450Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:24.450Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:24.451Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:24.451Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:38.805Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:38.808Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:39.036Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:39.036Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:39.037Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:44:39.304Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:16.200Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:16.202Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:16.439Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:16.439Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:16.440Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:16.678Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.087Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.089Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.309Z"}
{"level":"info","message":"Initializing Supabase database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.309Z"}
{"level":"info","message":"Initializing Supabase database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.309Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.552Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.553Z"}
{"error":"Unknown error","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.553Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.553Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.554Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.554Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.555Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:45:56.555Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:46:15 +0000] \"GET /health HTTP/1.1\" 200 194 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:46:15.996Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:48:00 +0000] \"GET /api/ai/health HTTP/1.1\" - - \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:48:00.475Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:48:20 +0000] \"GET /api/ai/health HTTP/1.1\" - - \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4768\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:48:20.796Z"}
{"body":{},"errorId":"err_1753922907085_e69hzuri3","level":"error","message":"Request error: Bad escaped character in JSON at position 11 (line 1 column 12)","method":"POST","params":{},"path":"/api/ai/chat","query":{},"service":"mobile-app-builder-backend","stack":"SyntaxError: Bad escaped character in JSON at position 11 (line 1 column 12)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)","timestamp":"2025-07-31T00:48:27.085Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:49:50 +0000] \"POST /api/ai/chat HTTP/1.1\" - - \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:49:50.774Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:52:03 +0000] \"POST /api/ai/chat HTTP/1.1\" - - \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:52:03.355Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:52:36 +0000] \"GET /health HTTP/1.1\" 200 195 \"-\" \"node-fetch\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:52:36.991Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:53:15 +0000] \"GET /api/ai/health HTTP/1.1\" - - \"-\" \"node-fetch\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:53:15.118Z"}
