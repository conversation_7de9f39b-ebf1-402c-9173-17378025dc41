"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.wsService = exports.server = exports.app = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const http_1 = require("http");
const migrations_1 = require("@/database/migrations");
const database_1 = require("@/config/database");
const websocket_1 = require("@/services/websocket");
const auth_1 = require("@/services/auth");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const auth_2 = __importDefault(require("@/routes/auth"));
const health_1 = __importDefault(require("@/routes/health"));
const ai_1 = __importDefault(require("@/routes/ai"));
const projects_1 = require("@/routes/projects");
const bundler_1 = require("@/routes/bundler");
const app = (0, express_1.default)();
exports.app = app;
const server = (0, http_1.createServer)(app);
exports.server = server;
const PORT = process.env.PORT || 3001;
const wsService = new websocket_1.WebSocketService(server);
exports.wsService = wsService;
app.use((0, helmet_1.default)({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "ws:", "wss:"],
        },
    },
}));
app.use((0, cors_1.default)({
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use((0, morgan_1.default)('combined', {
    stream: {
        write: (message) => {
            logger_1.logger.info(message.trim());
        }
    }
}));
app.use('/api/', rateLimiter_1.apiRateLimit);
app.use('/health', health_1.default);
app.use('/api/auth', auth_2.default);
app.use('/api/ai', ai_1.default);
app.use('/api/projects', (0, projects_1.createProjectsRouter)(database_1.pool));
app.use('/api/bundler', (0, bundler_1.createBundlerRouter)(database_1.pool, wsService));
app.get('/', (req, res) => {
    res.json({
        message: 'Mobile App Builder Platform API',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString(),
        endpoints: {
            health: '/health',
            auth: '/api/auth',
            ai: '/api/ai',
            projects: '/api/projects',
            bundler: '/api/bundler',
            docs: '/api/docs'
        }
    });
});
app.use(errorHandler_1.notFoundHandler);
app.use(errorHandler_1.errorHandler);
const gracefulShutdown = (signal) => {
    logger_1.logger.info(`Received ${signal}. Starting graceful shutdown...`);
    server.close(() => {
        logger_1.logger.info('HTTP server closed');
        logger_1.logger.info('Graceful shutdown completed');
        process.exit(0);
    });
    setTimeout(() => {
        logger_1.logger.error('Forced shutdown after timeout');
        process.exit(1);
    }, 30000);
};
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('uncaughtException', (error) => {
    logger_1.logger.error('Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
const startServer = async () => {
    try {
        logger_1.logger.info('Initializing database connection...');
        let databaseAvailable = false;
        try {
            await (0, database_1.initializeDatabase)();
            logger_1.logger.info('Running database migrations...');
            await (0, migrations_1.runMigrations)();
            logger_1.logger.info('Database initialized successfully');
            databaseAvailable = true;
        }
        catch (dbError) {
            logger_1.logger.warn('Database connection failed. Continuing without database...', {
                error: dbError instanceof Error ? dbError.message : 'Unknown error',
                mode: process.env.NODE_ENV || 'development'
            });
            logger_1.logger.warn('Features requiring database (auth, user management) will use in-memory fallbacks.');
            if (process.env.NODE_ENV === 'production' && process.env.REQUIRE_DATABASE === 'true') {
                throw dbError;
            }
        }
        setInterval(() => {
            auth_1.AuthService.cleanupExpiredTokens();
        }, 60 * 60 * 1000);
        server.listen(PORT, () => {
            logger_1.logger.info(`Server running on port ${PORT}`);
            logger_1.logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
            logger_1.logger.info(`WebSocket server initialized`);
            logger_1.logger.info(`Health check available at: http://localhost:${PORT}/health`);
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to start server:', error);
        process.exit(1);
    }
};
startServer();
//# sourceMappingURL=server.js.map