"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const logger_1 = require("@/utils/logger");
const auth_1 = require("@/services/auth");
const authenticateToken = async (req, res, next) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        res.status(401).json({
            error: 'Access token required'
        });
        return;
    }
    try {
        const user = await auth_1.AuthService.verifyToken(token);
        if (user) {
            req.user = user;
            next();
            return;
        }
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        req.user = {
            id: decoded.userId,
            email: decoded.email,
            name: '',
            subscriptionPlan: 'free',
            apiTokens: {},
            preferences: {
                theme: 'light',
                editorSettings: {
                    fontSize: 14,
                    tabSize: 2,
                    wordWrap: true,
                    minimap: true
                },
                defaultTemplate: 'basic',
                aiProvider: 'openai'
            },
            createdAt: new Date(),
            updatedAt: new Date()
        };
        next();
    }
    catch (error) {
        logger_1.logger.error('Token verification failed:', error);
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            res.status(401).json({
                error: 'Token expired',
                code: 'TOKEN_EXPIRED'
            });
        }
        else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            res.status(401).json({
                error: 'Invalid token',
                code: 'INVALID_TOKEN'
            });
        }
        else {
            res.status(401).json({
                error: 'Token verification failed'
            });
        }
    }
};
exports.authenticateToken = authenticateToken;
const optionalAuth = (req, res, next) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        next();
        return;
    }
    try {
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        req.user = {
            id: decoded.userId,
            email: decoded.email,
            name: '',
            subscriptionPlan: 'free',
            apiTokens: {},
            preferences: {
                theme: 'light',
                editorSettings: {
                    fontSize: 14,
                    tabSize: 2,
                    wordWrap: true,
                    minimap: true
                },
                defaultTemplate: 'basic',
                aiProvider: 'openai'
            },
            createdAt: new Date(),
            updatedAt: new Date()
        };
        next();
    }
    catch (error) {
        logger_1.logger.warn('Optional auth failed, continuing without authentication:', error);
        next();
    }
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.js.map